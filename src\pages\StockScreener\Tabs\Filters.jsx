import React from "react";
import { Table } from "antd";
import { useSelector } from "react-redux";

export default function Filters({ data }) {
  const selectedFilters = useSelector(state => state.filters.selectedFilters);

  const columns = [
    { title: "Symbol", dataIndex: "symbol", key: "symbol" },
    { title: "Company", dataIndex: "company", key: "company" },
    ...selectedFilters.map(filterName => ({
      title: filterName,
      dataIndex: filterName,
      key: filterName,
      render: (val) => val != null ? val : "--"
    }))
  ];

  return (
    <div style={{ overflowX: "auto" }}>
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        rowKey="symbol"
        scroll={{ x: "max-content" }} // Enables horizontal scroll when needed
      />
    </div>
  );
}
