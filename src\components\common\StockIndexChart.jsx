import { Line } from "react-chartjs-2";
import { Chart as ChartJS, LineElement, CategoryScale, LinearScale, PointElement,TimeScale,Tooltip, Title,Legend } from "chart.js";
import { CandlestickController, CandlestickElement } from 'chartjs-chart-financial';
ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement, CandlestickController, CandlestickElement,TimeScale,Tooltip, Title,Legend);

const StockIndexChart = () => {
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        elements: {
          line: {
            borderWidth: 2,
            tension: 0.3,
          },
          point: {
            radius: 0,
          },
        },
        scales: {
            x: { display: false },
            y: { display: false },
        },
        plugins: {
          title: {
            display: false,
          },
          legend: { display: false },
          tooltip: { enabled: false },
        },
    };
    
    const tasiData = {
        labels: Array(30).fill(""),
        datasets: [
          {
            label: 'My First Dataset',
            data: [40, 42, 45, 44, 43, 47, 46, 50, 49, 48, 47, 46, 45, 42, 41, 40, 39, 38, 37, 36, 34, 32, 30, 28, 27, 26, 25, 24, 23, 22],
            borderColor: "red",
            fill: false,
            pointRadius: 0,
            pointHoverRadius: 0,
            borderWidth: 2,
          },
        ],
    };
    
    const nomuData = {
        labels: Array(30).fill(""),
        datasets: [
          {
            data: [20, 22, 24, 23, 25, 27, 29, 30, 32, 33, 31, 29, 27, 26, 28, 30, 34, 36, 37, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58],
            borderColor: "green",
            fill: false,
            pointRadius: 0,
            pointHoverRadius: 0,
            borderWidth: 2,
          },
        ],
    };
    
    return (
        <div className="flex">
            <div className="flex-1 px-2 border text-black dark:text-white">
                <p className="font-bold">Tasi</p>
                <p className="text-red-500 text-sm">&#8595; -0.5%</p>
                <div className="h-10">
                    <Line data={tasiData} options={chartOptions} />
                </div>
            </div>
            <div className="flex-1 px-2 border text-black dark:text-white">
                <p className="font-bold">Nomu</p>
                <p className="text-green-500 text-sm">&#8593; +1%</p>
                <div className="h-10">
                    <Line data={nomuData} options={chartOptions} />
                </div>
            </div>
        </div>
    );
};

export default StockIndexChart;
