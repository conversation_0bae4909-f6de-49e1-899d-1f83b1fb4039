import MainContentHeader from "../../components/common/MainContentHeader";

const newsData = [
  {
    title:
      "U.S. dollar strength to remain; Fed to hold interest rates until December: Oxford Economics",
    description:
      "Oxford Economics discusses the impact of the strong U.S. dollar and interest rate expectations.",
    videoUrl: "https://www.youtube.com/embed/sample1",
  },
  {
    title:
      "China’s deflation problem drives bond yields lower despite more issuance: Strategist",
    description:
      "JPMorgan's strategist discusses how China's deflation is impacting bond markets.",
    videoUrl: "https://www.youtube.com/embed/sample2",
  },
  {
    title: "Market opportunities and tariff risks",
    description:
      "Yahoo Finance experts discuss investment strategies amid tariff uncertainties.",
    videoUrl: "https://www.youtube.com/embed/sample3",
  },
  {
    title:
      "U.S. dollar strength to remain; Fed to hold interest rates until December: Oxford Economics",
    description:
      "Oxford Economics discusses the impact of the strong U.S. dollar and interest rate expectations.",
    videoUrl: "https://www.youtube.com/embed/sample1",
  },
  {
    title:
      "China’s deflation problem drives bond yields lower despite more issuance: Strategist",
    description:
      "JPMorgan's strategist discusses how China's deflation is impacting bond markets.",
    videoUrl: "https://www.youtube.com/embed/sample2",
  },
  {
    title: "Market opportunities and tariff risks",
    description:
      "Yahoo Finance experts discuss investment strategies amid tariff uncertainties.",
    videoUrl: "https://www.youtube.com/embed/sample3",
  },
];

const Index = () => {
  return (
    <>
      <MainContentHeader />
      <YouTubeVideo />
      <NewsComponent />
    </>
  );
};

export default Index;

const NewsComponent = () => {
  return (
    <div className="p-4 grid grid-cols-1 gap-6">
      {newsData.map((news, index) => (
        <div
          key={index}
          className="flex flex-col md:flex-row bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden"
        >
          <div className="w-full md:w-1/3">
            <iframe
              className="w-full h-48 md:h-full"
              src={news.videoUrl}
              title={news.title}
              allowFullScreen
            ></iframe>
          </div>
          <div className="w-full md:w-2/3 p-4 flex flex-col justify-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {news.title}
            </h2>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              {news.description}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

const YouTubeVideo = () => {
  return (
    <div className="flex flex-col items-center p-2">
      <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
        Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, whe
      </h1>
      <div className="w-full h-[500px]">
        <iframe
          width="100%"
          height="500"
          src="https://www.youtube.com/embed/dQw4w9WgXcQ" // Replace with your video ID
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="rounded-lg shadow-lg"
        ></iframe>
      </div>
      <p className="mt-4 text-lg text-gray-700 dark:text-gray-300 text-start">
        This video covers everything you need to know about building a YouTube component in React using Tailwind CSS. Learn how to style, embed videos, and optimize your UI!
      </p>
    </div>
  );
};
