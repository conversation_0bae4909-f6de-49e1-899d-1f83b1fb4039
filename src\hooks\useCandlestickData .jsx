import { useEffect, useRef, useState } from 'react';
import useTwelveDataWebSocket from './useTwelveDataWebSocket';

const useCandlestickData = (symbol, interval = 60_000) => {
  const [candles, setCandles] = useState([]);
  const currentCandleRef = useRef(null);
  const { latestPrice, status, error } = useTwelveDataWebSocket(symbol);

  // Simulate volume since Twelve Data WebSocket doesn't provide it
  const volumeRef = useRef(0);
  const [volumeData, setVolumeData] = useState([]);

  useEffect(() => {
    if (!latestPrice || status !== 'connected') return;

    const timestamp = Math.floor(Date.now() / 1000); // Convert to seconds
    const price = parseFloat(latestPrice);
    const bucket = Math.floor(timestamp / (interval / 1000)) * (interval / 1000);

    // Simulate volume (replace with real volume data if available)
    volumeRef.current += Math.floor(Math.random() * 1000);

    if (
      !currentCandleRef.current ||
      currentCandleRef.current.time !== bucket
    ) {
      if (currentCandleRef.current) {
        setCandles((prev) => {
          const newCandles = [...prev, { ...currentCandleRef.current, volume: volumeRef.current }];
          return newCandles
            .filter(
              (candle, index, self) =>
                index === self.findIndex((c) => c.time === candle.time)
            )
            .sort((a, b) => a.time - b.time)
            .slice(-100);
        });
        setVolumeData((prev) => [
          ...prev,
          { time: currentCandleRef.current.time, value: volumeRef.current },
        ]);
        volumeRef.current = 0; // Reset volume for the new candle
      }

      currentCandleRef.current = {
        time: bucket, // Unix timestamp in seconds
        open: price,
        high: price,
        low: price,
        close: price,
      };
    } else {
      currentCandleRef.current = {
        ...currentCandleRef.current,
        high: Math.max(currentCandleRef.current.high, price),
        low: Math.min(currentCandleRef.current.low, price),
        close: price,
      };
    }
  }, [latestPrice, interval, status]);

  return {
    candles,
    volumeData,
    latestPrice,
    status,
    error,
  };
};

export default useCandlestickData;