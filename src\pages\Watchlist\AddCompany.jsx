import React, { useState, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import debounce from 'lodash.debounce';
import { Input, Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import WatchlistSelectModal from './WatchlistSelectModal';
import { addStock } from '../../redux/watchlist/watchlistSlice';
import { setSearchTerm, clearSearch } from '../../redux/search/searchSlice';

export default function AddCompany({ instanceId }) {
  const dispatch = useDispatch();
  const allStocks = useSelector((s) => s.stocks.data);
  const { term, show, activeInstance } = useSelector((s) => s.search);

  const [localTerm, setLocalTerm] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSymbol, setSelectedSymbol] = useState(null);

  const debouncedSet = useMemo(
    () => debounce((v) => dispatch(setSearchTerm({ term: v, instanceId })), 300),
    [dispatch, instanceId]
  );

  useEffect(() => () => debouncedSet.cancel(), [debouncedSet]);

  const results = useMemo(() => {
    if (!term || activeInstance !== instanceId) return [];
    const q = term.toLowerCase();
    return allStocks
      .filter(
        (s) =>
          s.symbol.toLowerCase().includes(q) ||
          s.name.toLowerCase().includes(q)
      )
      .slice(0, 10);
  }, [allStocks, term, activeInstance, instanceId]);

  const onInputChange = (e) => {
    const v = e.target.value;
    setLocalTerm(v);
    debouncedSet(v);
  };
  const onFocus = () => dispatch(setSearchTerm({ term: localTerm, instanceId }));
  const onSelectSuggestion = (symbol) => {
    dispatch(clearSearch());
    setLocalTerm('');
    setSelectedSymbol(symbol);
    setModalVisible(true);
  };
  const handleModalConfirm = (watchlistId) => dispatch(addStock({ watchlistId, symbol: selectedSymbol }));

  return (
    <>
      <div className="flex justify-center mt-6">
        {/* Wrapper for label + search */}
        <div className="flex border border-black rounded w-full max-w-lg overflow-hidden">
          {/* Label div */}
          <div className="px-4 py-2 bg-black text-white font-semibold">
            My Watchlist
          </div>

          {/* Search div */}
          <div className="flex items-center flex-1">
            <Input
              placeholder="Add New Company..."
              value={localTerm}
              onChange={onInputChange}
              onFocus={onFocus}
              bordered={false}
              className="w-full px-4 py-2 text-sm text-black placeholder-gray-500 bg-white"
            />
            <button
              onClick={() => debouncedSet.flush()}
              className="px-3 text-black"
            >
              <SearchOutlined />
            </button>
          </div>
        </div>
      </div>

      {show && activeInstance === instanceId && results.length > 0 && (
        <ul className="absolute mt-1 w-full max-w-lg bg-white shadow-lg rounded-md z-50 left-1/2 transform -translate-x-1/2">
          {results.map((s) => (
            <li
              key={s.symbol}
              onClick={() => onSelectSuggestion(s.symbol)}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-black"
            >
              {s.name} ({s.symbol})
            </li>
          ))}
        </ul>
      )}

      <WatchlistSelectModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={handleModalConfirm}
      />
    </>
  );
}
