import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
} from "chart.js";
import { useTranslation } from "react-i18next";

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement);

const MarketSummaryCharts = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    elements: {
      line: { borderWidth: 2 },
      point: { radius: 0 },
    },
    scales: {
      x: {
        ticks: { display: false, callback: () => "" },
        grid: { display: false, drawTicks: false },
      },
      y: {
        ticks: { display: false, callback: () => "" },
        grid: { display: false, drawTicks: false },
      },
    },
    plugins: {
      legend: { display: false },
      tooltip: { enabled: false },
    },
  };

  const tasiData = {
    labels: Array(20).fill(""),
    datasets: [
      {
        data: [
          40, 39, 38, 37, 36, 35, 36, 37, 38, 35, 34, 33, 32, 31, 30, 31, 32,
          30, 29, 28,
        ],
        borderColor: "red",
        fill: false,
        pointRadius: 0,
        borderWidth: 2,
      },
    ],
  };

  const numoData = {
    labels: Array(20).fill(""),
    datasets: [
      {
        data: [
          28, 29, 30, 31, 32, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42,
          43, 44, 45,
        ],
        borderColor: "green",
        fill: false,
        pointRadius: 0,
        borderWidth: 2,
      },
    ],
  };

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 gap-8 text-center ${
        isRTL ? "rtl" : "ltr"
      }`}
    >
      {/* Tasi Box */}
      <div className="border border-gray-200 dark:border-gray-700 p-4 w-64 bg-white dark:bg-gray-800 rounded-lg">
        <p className="font-bold">
          {t("market.tasi")}
          <span className="text-black dark:text-white">11,586</span>{" "}
          <span className="text-red-500">(-12) (-0.5%)</span>
        </p>
        <p>
          {t("market.companies")} <span className="text-green-500">48</span>{" "}
          <span>5</span> <span className="text-red-500">156</span>{" "}
          <span>(209)</span>
        </p>
        <p>
          {t("market.value")}{" "}
          <span className="text-red-500">5,345,589,694</span>
        </p>
        <p>
          {t("market.volume")} <span className="text-red-500">589,694</span>
        </p>
        <div className="border-t border-gray-200 dark:border-gray-700 mt-2 h-12">
          <Line data={tasiData} options={chartOptions} />
        </div>
      </div>

      {/* Numo Box */}
      <div className="border border-gray-200 dark:border-gray-700 p-4 w-64 bg-white dark:bg-gray-800 rounded-lg">
        <p className="font-bold">
          {t("market.numo")}{" "}
          <span className="text-black dark:text-white">36,886</span>{" "}
          <span className="text-green-500">(+156) (+1%)</span>
        </p>
        <p>
          {t("market.companies")} <span className="text-green-500">56</span>{" "}
          <span>10</span> <span className="text-red-500">26</span>{" "}
          <span>(92)</span>
        </p>
        <p>
          {t("market.value")}{" "}
          <span className="text-green-500">345,589,694</span>
        </p>
        <p>
          {t("market.volume")} <span className="text-green-500">1,150</span>
        </p>
        <div className="border-t border-gray-200 dark:border-gray-700 mt-2 h-12">
          <Line data={numoData} options={chartOptions} />
        </div>
      </div>
    </div>
  );
};

export default MarketSummaryCharts;
