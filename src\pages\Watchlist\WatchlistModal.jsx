import React, { useState, useEffect } from "react";

const WatchlistModal = ({ isOpen, onClose, onSave }) => {
  const [watchlistName, setWatchlistName] = useState("");

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  const handleSave = () => {
    if (watchlistName.trim()) {
      onSave(watchlistName);
      setWatchlistName("");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="bg-white p-6 rounded-lg shadow-xl w-96 transform transition-all duration-300 pointer-events-auto">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Create a New Watchlist</h2>

        <input
          type="text"
          value={watchlistName}
          onChange={(e) => setWatchlistName(e.target.value)}
          placeholder="Enter watchlist name"
          className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />

        <div className="flex justify-end mt-4 space-x-3">
          <button
            onClick={onClose}
            className="px-5 py-2 text-gray-600 font-medium bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            Cancel
          </button>

          <button
            onClick={handleSave}
            className="px-5 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default WatchlistModal;