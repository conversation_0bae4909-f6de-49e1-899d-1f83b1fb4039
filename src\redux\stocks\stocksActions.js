import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  fetchStocksList,
  fetchStockHeader,
  fetchTimeSeriesData,
  fetchStockQuotesBySymbols,
  fetchTasiStocks,
  fetchNumoStocks,
  fetchAllMarketStocks,
  fetchIndustries,
  fetchStocksByIndustry,
} from "./stocksService";

export const fetchStocks = createAsyncThunk("stocks/fetchStocks", async () => {
  return await fetchStocksList();
});

export const fetchStockHeaderBySymbol = createAsyncThunk(
  "stocks/fetchStockHeaderBySymbol",
  async (symbol, { rejectWithValue }) => {
    try {
      return await fetchStockHeader(symbol);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

export const getTimeSeriesData = createAsyncThunk(
  "stocks/getTimeSeriesData",
  async ({ symbol, interval, outputsize }, { rejectWithValue }) => {
    try {
      return await fetchTimeSeriesData(symbol, interval, outputsize);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

export const fetchQuotesBySymbols = createAsyncThunk(
  "stocks/fetchQuotesBySymbols",
  async (symbols, { rejectWithValue }) => {
    try {
      return await fetchStockQuotesBySymbols(symbols);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

export const fetchTasi = createAsyncThunk("stocks/fetchTasi", async () => {
  return await fetchTasiStocks();
});

export const fetchNumo = createAsyncThunk("stocks/fetchNumo", async () => {
  return await fetchNumoStocks();
});

export const fetchAllMarket = createAsyncThunk(
  "stocks/fetchAllMarket",
  async () => {
    return await fetchAllMarketStocks();
  }
);

// New: fetch industries list
export const fetchIndustriesList = createAsyncThunk(
  "stocks/fetchIndustriesList",
  async () => await fetchIndustries()
);

// New: fetch by industry
export const fetchByIndustry = createAsyncThunk(
  "stocks/fetchByIndustry",
  async (industry) => await fetchStocksByIndustry(industry)
);

