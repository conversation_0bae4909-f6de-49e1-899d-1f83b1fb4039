import PropTypes from "prop-types";
// import { useTranslation } from "react-i18next";

const StockInfoCard = ({ stockData }) => {
  //   const { t } = useTranslation();

  const { name, ticker, price, currency, change, changePercent, timestamp } =
    stockData;

  // Determine color based on price change
  const changeColor = change >= 0 ? "text-green-500" : "text-red-500";

  return (
    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm text-gray-900 dark:text-white w-full">
      <div className="flex items-center space-x-3">
        <div className="h-11 w-11 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
          {ticker.substring(0, 2)}
        </div>

        <div>
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {name}
            </h3>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-600 dark:text-gray-300 text-sm">
              {ticker}
            </span>
            <span className="text-gray-500 dark:text-gray-400 mx-1">|</span>
            <span className="text-gray-900 dark:text-white font-medium">
              {price} {currency}
            </span>
            <span className={`ml-1 ${changeColor}`}>
              {change > 0 ? "+" : ""}
              {change} ({change > 0 ? "+" : ""}
              {changePercent}%)
            </span>
          </div>
        </div>
      </div>

      <div className="text-xs text-gray-500 dark:text-gray-400">
        {timestamp}
      </div>
    </div>
  );
};

StockInfoCard.propTypes = {
  stockData: PropTypes.shape({
    name: PropTypes.string.isRequired,
    ticker: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    currency: PropTypes.string.isRequired,
    change: PropTypes.number.isRequired,
    changePercent: PropTypes.number.isRequired,
    timestamp: PropTypes.string.isRequired,
  }).isRequired,
};

export default StockInfoCard;
