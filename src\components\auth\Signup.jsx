import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { registerUser } from "../../redux/user/userActions";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";

const Signup = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  const [formData, setFormData] = useState({
    firstname: "",
    email: "",
    password: "",
    confirmPassword: "",
    termsAccepted: false,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      toast.error(t("auth.passwordsDontMatch"));
      return;
    }
    const processedData = { ...formData, email: formData.email.toLowerCase() };
    try {
      await dispatch(
        registerUser({
          firstName: processedData.firstname,
          email: processedData.email,
          password: processedData.password,
        })
      ).unwrap();
      toast.success(t("auth.registrationSuccessful"));
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (error) {
      toast.error(error);
    }
  };

  const handleGooglePassport = () => {
    window.location.href = `${import.meta.env.VITE_BACKEND_URL}/auth/google`;
  };

  return (
    <section className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={isRTL}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        className="bg-white shadow-lg rounded-lg p-4"
        bodyClassName="text-gray-800 dark:text-white"
        progressClassName="bg-green-500"
      />
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:min-h-screen lg:py-0">
        <div className="flex items-center justify-between w-full max-w-md mb-6">
          <a
            href="#"
            className="flex items-center text-2xl font-semibold text-gray-900 dark:text-white"
          >
            <div className="text-2xl font-bold flex gap-2 items-center">
              <div className="w-10 h-10 bg-black/10 rounded-full" />
              {t("app.title")}
            </div>
          </a>
          <LanguageSwitcher />
        </div>
        <div className="w-full bg-white dark:bg-gray-800 rounded-lg shadow dark:border sm:max-w-md">
          <div className="p-6 space-y-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              {t("auth.createAccount")}
            </h1>
            <div className="flex items-center justify-center">
              <button
                onClick={handleGooglePassport}
                className="flex items-center justify-center w-full max-w-sm px-4 py-3 
             bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
             rounded-lg shadow hover:bg-gray-100 dark:hover:bg-gray-700 transition duration-200"
              >
                <svg
                  className="w-6 h-6 mx-3"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 18 18"
                >
                  <path
                    fill="#4285F4"
                    d="M9 3.48c1.69 0 2.84.73 3.5 1.35l2.57-2.57C13.24 1.27 11.26 0 9 0 5.48 0 2.43 2.16 1 5.39l3.04 2.36C4.85 5.44 6.64 3.48 9 3.48z"
                  />
                  <path
                    fill="#34A853"
                    d="M17.64 9.2c0-.63-.06-1.25-.18-1.84H9v3.48h4.84c-.21 1.11-.84 2.06-1.79 2.7l2.86 2.22c1.67-1.54 2.64-3.81 2.64-6.56z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M3.97 10.57a5.42 5.42 0 0 1 0-3.44V4.77H1.01a8.98 8.98 0 0 0 0 8.46l2.96-2.66z"
                  />
                  <path
                    fill="#EA4335"
                    d="M9 18c2.43 0 4.47-.8 5.96-2.18l-2.86-2.22a5.68 5.68 0 0 1-3.1.93 5.42 5.42 0 0 1-5.1-3.74H1.01l-2.96 2.66C2.43 15.84 5.48 18 9 18z"
                  />
                </svg>
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {t("auth.signUpWithGoogle")}
                </span>
              </button>
            </div>
            <div className="flex items-center">
              <div className="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
              <span className="px-3 text-gray-500 dark:text-gray-300">
                {t("auth.or")}
              </span>
              <div className="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
            </div>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div>
                <label
                  htmlFor="firstname"
                  className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                >
                  {t("auth.userName")}
                </label>
                <input
                  type="text"
                  name="firstname"
                  id="firstname"
                  value={formData.firstname}
                  onChange={handleChange}
                  className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg block w-full p-2.5 text-gray-900 dark:text-white"
                  placeholder="john doe"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="email"
                  className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                >
                  {t("auth.yourEmail")}
                </label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg block w-full p-2.5 text-gray-900 dark:text-white"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="password"
                  className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                >
                  {t("auth.password")}
                </label>
                <input
                  type="password"
                  name="password"
                  id="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="••••••••"
                  className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg block w-full p-2.5 text-gray-900 dark:text-white"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="confirmPassword"
                  className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                >
                  {t("auth.confirmPassword")}
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  id="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="••••••••"
                  className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg block w-full p-2.5 text-gray-900 dark:text-white"
                  required
                />
              </div>
              <div className="flex items-start">
                <div className="flex items-center">
                  <input
                    id="terms"
                    name="termsAccepted"
                    type="checkbox"
                    checked={formData.termsAccepted}
                    onChange={handleChange}
                    className="w-4 h-4 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-700"
                    required
                  />
                  <label
                    htmlFor="terms"
                    className="mx-2 text-sm text-gray-500 dark:text-gray-300"
                  >
                    {t("auth.acceptTerms")}{" "}
                    <a
                      href="#"
                      className="font-medium text-primary-600 hover:underline"
                    >
                      {t("auth.termsAndConditions")}
                    </a>
                  </label>
                </div>
              </div>
              <button
                type="submit"
                className="w-full text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 font-medium rounded-lg text-sm px-5 py-2.5 shadow-lg transition-all duration-300 cursor-pointer"
              >
                {t("auth.createAccount")}
              </button>
              <p className="text-sm font-light text-gray-500 dark:text-gray-300">
                {t("auth.alreadyHaveAccount")}{" "}
                <Link
                  to="/login"
                  className="font-medium text-primary-600 hover:underline"
                >
                  {t("auth.loginHere")}
                </Link>
              </p>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Signup;
