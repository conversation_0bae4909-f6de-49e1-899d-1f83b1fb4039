import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faGlobe } from "@fortawesome/free-solid-svg-icons";

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);
  const isRTL = i18n.dir() === "rtl";
  const currentLang = i18n.language || "en";

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    setIsLangDropdownOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}
        className="h-8 w-8 flex items-center justify-center text-gray-500 dark:text-gray-300"
        title="Change Language"
      >
        <FontAwesomeIcon icon={faGlobe} />
      </button>
      {isLangDropdownOpen && (
        <div
          className={`absolute ${
            isRTL ? "left-0" : "right-0"
          } mt-2 w-24 bg-white dark:bg-gray-600 shadow-md rounded-md z-20`}
        >
          <ul className="py-2">
            <li
              className={`px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-gray-900 dark:text-white ${
                currentLang === "en" ? "bg-gray-100 dark:bg-gray-700" : ""
              }`}
              onClick={() => changeLanguage("en")}
            >
              English
            </li>
            <li
              className={`px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-gray-900 dark:text-white ${
                currentLang === "ar" ? "bg-gray-100 dark:bg-gray-700" : ""
              }`}
              onClick={() => changeLanguage("ar")}
            >
              العربية
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
