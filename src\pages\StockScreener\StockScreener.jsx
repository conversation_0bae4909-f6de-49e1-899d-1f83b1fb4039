// src/components/Screener/StockScreener.jsx
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Tabs, Pagination } from 'antd';
import {
  loadScreenerOverview,
  loadPerformance,
  loadEarnings
} from '../../redux/screener/screenerSlice';

import Overview from './Tabs/Overview';
import Performance from './Tabs/Performance';
import Earnings from './Tabs/Earnings';
import Filters from './Tabs/Filters';

const { TabPane } = Tabs;

export default function StockScreener() {
  const dispatch = useDispatch();
  const { overview, performance, earnings } = useSelector(s => s.screener);
  const selectedFilters = useSelector(s => s.filters.selectedFilters);

  const [activeTab, setActiveTab] = useState('overview');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // Load overview on mount
  useEffect(() => {
    dispatch(loadScreenerOverview());
  }, [dispatch]);

  // On tab change, reset page and load data if needed
  const handleTabChange = (key) => {
    setActiveTab(key);
    setCurrentPage(1);

    if (key === 'performance') {
      dispatch(loadPerformance());
    } else if (key === 'earnings') {
      dispatch(loadEarnings());
    }
  };

  // Sanitize generic record
  const sanitize = (rec, idx) => {
    const safe = {};
    Object.entries(rec).forEach(([k, v]) => {
      safe[k] = v == null ? '' : v;
    });
    safe.key = safe.symbol || idx;
    return safe;
  };

  // Build paginated Overview data
  const makeOverviewData = () => {
    return overview
      .map((s, i) => {
        const sym  = s.symbol   ?? '';
        const nm   = s.name     ?? '—';
        const pNum = parseFloat(s.price);
        const cNum = parseFloat(s.change    ?? '0');
        const dNum = parseFloat(s.Div_yield ?? '0');
        const hNum = parseFloat(s._52High   ?? '0');
        const lNum = parseFloat(s._52Low    ?? '0');
        const mNum = parseFloat(s.Market_Cap);

        return {
          key: sym || i,
          symbol: sym,
          company: nm,
          price: !isNaN(pNum) ? `${pNum.toLocaleString('en-US',{minimumFractionDigits:2})} SAR` : 'N/A',
          dayChange: !isNaN(cNum) ? `${cNum.toFixed(2)}%` : '0%',
          divYield: !isNaN(dNum) ? `${dNum.toFixed(2)}%` : '0%',
          peRatio: s.pe_ratio ?? 'N/A',
          high52: !isNaN(hNum) ? `${hNum.toLocaleString('en-US',{minimumFractionDigits:2})} SAR` : 'N/A',
          low52:  !isNaN(lNum) ? `${lNum.toLocaleString('en-US',{minimumFractionDigits:2})} SAR` : 'N/A',
          marketCap: !isNaN(mNum) ? `${(mNum/1e9).toFixed(2)}B SAR` : '-',
          industry: s.industry ?? '—',
        };
      })
      .slice((currentPage - 1)*pageSize, currentPage*pageSize);
  };

  // Build paginated Performance data
  const makePerformanceData = () =>
    performance
      .map((r,i) => sanitize(r,i))
      .slice((currentPage - 1)*pageSize, currentPage*pageSize);

  // Build paginated Earnings data
  const makeEarningsData = () =>
    earnings
      .map((r,i) => sanitize(r,i))
      .slice((currentPage - 1)*pageSize, currentPage*pageSize);

  // Build paginated Filters data (symbol, company + each selected filter)
  const makeFiltersData = () => {
    const full = overview.map(s => {
      const base = {
        key: s.symbol,
        symbol: s.symbol,
        company: s.name ?? '—',
      };
      selectedFilters.forEach(filterName => {
        base[filterName] = s[filterName] ?? '';
      });
      return base;
    });
    return full.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  };

  // Decide which data slice to show
  const dataToShow =
    activeTab === 'overview'     ? makeOverviewData() :
    activeTab === 'performance'  ? makePerformanceData() :
    activeTab === 'earnings'     ? makeEarningsData() :
                                    makeFiltersData();

  // Determine total count for pagination
  const totalCount =
    activeTab === 'overview'     ? overview.length :
    activeTab === 'performance'  ? performance.length :
    activeTab === 'earnings'     ? earnings.length :
                                    overview.length;

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        destroyInactiveTabPane={true}
      >
        <TabPane tab="Overview" key="overview">
          <Overview
            data={dataToShow}
            selectedRowKeys={selectedRowKeys}
            onChange={setSelectedRowKeys}
          />
        </TabPane>
        <TabPane tab="Performance" key="performance">
          <Performance
            data={dataToShow}
            selectedRowKeys={selectedRowKeys}
            onChange={setSelectedRowKeys}
          />
        </TabPane>
        <TabPane tab="Earnings" key="earnings">
          <Earnings
            data={dataToShow}
            selectedRowKeys={selectedRowKeys}
            onChange={setSelectedRowKeys}
          />
        </TabPane>
        <TabPane tab="Filters" key="filters">
          <Filters data={dataToShow} />
        </TabPane>
      </Tabs>

      <div className="mt-4 flex justify-center">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={setCurrentPage}
          showSizeChanger={false}
        />
      </div>
    </div>
  );
}
