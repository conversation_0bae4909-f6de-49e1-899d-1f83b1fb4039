// src/components/AppHeader.jsx
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useGetUserDetailsQuery } from "../../redux/user/userService";
import { setCredentials } from "../../redux/user/userSlice";
import { userLogout } from "../../redux/user/userActions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBell,
  faUser,
  faCaretDown,
  faMoon,
  faSun,
  faGlobe,
} from "@fortawesome/free-solid-svg-icons";
import { NavLink, useNavigate } from "react-router-dom";
import { toggleDarkMode } from "../../redux/darkMode/darkModeSlice";
import { useTranslation } from "react-i18next";
// import SearchBox from "../common/SearchBox";
import { Search } from "lucide-react";
const AppHeader = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo } = useSelector((state) => state.user);
  const darkModeEnabled = useSelector((state) => state.darkMode.enabled);
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  // Fetch user details
  const { data } = useGetUserDetailsQuery("userDetails", {
    pollingInterval: 900000, // 15 minutes
  });

  useEffect(() => {
    if (data) {
      dispatch(setCredentials(data));
    }
  }, [data, dispatch]);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] =
    useState(false);
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);
  const toggleNotificationDropdown = () =>
    setIsNotificationDropdownOpen(!isNotificationDropdownOpen);
  const toggleLangDropdown = () => setIsLangDropdownOpen(!isLangDropdownOpen);

  const handleLogout = async () => {
    try {
      await dispatch(userLogout()).unwrap();
      navigate("/");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    setIsLangDropdownOpen(false);
  };

  // Get current language
  const currentLang = i18n.language || "en";

  return (
    <header className="w-full bg-white dark:bg-gray-800 shadow p-4 sticky z-10">
      <nav className="flex justify-between items-center">
        {/* Logo & Title */}
        <div className="text-2xl font-bold flex gap-2 items-center text-gray-900 dark:text-white">
          <div className="w-10 h-10 bg-black/10 rounded-full" />
          {t("app.title")}
        </div>

        {/* Search bar and icons */}
        <div className="flex items-center gap-4">
          {/* Search input */}
          {/* <input
            type="text"
            placeholder={t("header.search")}
            className="hidden md:block px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring focus:border-black-300 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          /> */}
{/* <SearchBox instanceId="header" /> */}
{/* <SearchBox searchContext="header" /> */}

          {/* Language Switcher */}
          <div className="relative">
            <button
              onClick={toggleLangDropdown}
              className="h-8 w-8 flex items-center justify-center text-gray-500 dark:text-gray-300"
              title="Change Language"
            >
              <FontAwesomeIcon icon={faGlobe} />
            </button>
            {isLangDropdownOpen && (
              <div
                className={`absolute ${
                  isRTL ? "left-0" : "right-0"
                } mt-4 w-24 bg-white dark:bg-gray-600 shadow-md rounded-md z-20`}
              >
                <ul className="py-2">
                  <li
                    className={`px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-gray-900 dark:text-white ${
                      currentLang === "en" ? "bg-gray-100 dark:bg-gray-700" : ""
                    }`}
                    onClick={() => changeLanguage("en")}
                  >
                    English
                  </li>
                  <li
                    className={`px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-gray-900 dark:text-white ${
                      currentLang === "ar" ? "bg-gray-100 dark:bg-gray-700" : ""
                    }`}
                    onClick={() => changeLanguage("ar")}
                  >
                    العربية
                  </li>
                </ul>
              </div>
            )}
          </div>

          {/* Dark Mode toggle */}
          <button
            onClick={() => dispatch(toggleDarkMode())}
            className="h-8 w-8 flex items-center justify-center text-gray-500 dark:text-gray-300"
            title="Toggle Dark Mode"
          >
            <FontAwesomeIcon icon={darkModeEnabled ? faSun : faMoon} />
          </button>

          {/* Notification Icon */}
          <div className="relative flex items-center">
            <button
              className="h-8 w-8 text-gray-500 dark:text-gray-300"
              onClick={toggleNotificationDropdown}
            >
              <FontAwesomeIcon icon={faBell} />
              <span className="absolute top-0 right-0 bg-black dark:bg-gray-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                2
              </span>
            </button>
            {isNotificationDropdownOpen && (
              <div
                className={`absolute top-8 ${
                  isRTL ? "left-0" : "right-0"
                } mt-4 z-20 w-96 bg-white dark:bg-gray-700 rounded-xl shadow flex-col justify-start items-start inline-flex`}
              >
                <div className="p-4 text-gray-900 dark:text-white">
                  {t("header.notifications")}
                </div>
              </div>
            )}
          </div>

          {/* {userInfo && userInfo.role?.includes("admin") && (
            <NavLink
              to="/admin"
              className="px-3 py-2 bg-blue-500 text-white rounded-md shadow hover:bg-blue-600 transition-colors duration-200"
              title="Admin Dashboard"
            >
              Admin
            </NavLink>
          )} */}

          {/* Profile Dropdown */}
          <div className="relative">
            {userInfo ? (
              <>
                <button
                  className="flex items-center space-x-2"
                  onClick={toggleDropdown}
                >
                  <FontAwesomeIcon
                    icon={faUser}
                    className="text-gray-500 dark:text-gray-300"
                  />
                  <span className="hidden md:block font-medium text-gray-900 dark:text-white">
                    {userInfo?.userName || "User"}
                  </span>
                  <FontAwesomeIcon
                    icon={faCaretDown}
                    className="text-gray-500 dark:text-gray-300"
                  />
                </button>
                {isDropdownOpen && (
                  <div className="absolute right-0 mt-4 w-48 bg-white dark:bg-gray-800 shadow-md rounded-md z-20">
                    <ul className="py-2">
                      <li className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                        <NavLink
                          to="/user-profile"
                          onClick={() => setIsDropdownOpen(false)}
                          className="block text-gray-900 dark:text-white"
                        >
                          {t("header.profile")}
                        </NavLink>
                      </li>
                      <li className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-gray-900 dark:text-white">
                        {t("header.settings")}
                      </li>
                      <li
                        className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                        onClick={handleLogout}
                      >
                        <button className="cursor-pointer text-gray-900 dark:text-white">
                          {t("header.logout")}
                        </button>
                      </li>
                    </ul>
                  </div>
                )}
              </>
            ) : (
              <NavLink
                className="button text-gray-900 dark:text-white"
                to="/login"
              >
                {t("header.login")}
              </NavLink>
            )}
          </div>
        </div>
      </nav>
    </header>
  );
};

export default AppHeader;