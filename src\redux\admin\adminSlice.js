import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isAdmin: false,
  adminData: null, 
};

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    setAdminStatus(state, action) {
      console.log('Redux setAdminStatus:', action.payload);

      state.isAdmin = action.payload;
    },
    setAdminData(state, action) {
      state.adminData = action.payload;
    },
  },
});

export const { setAdminStatus, setAdminData } = adminSlice.actions;
export default adminSlice.reducer;
