import { useState, useEffect } from "react";
import { Table, Radio, Spin, Alert, Tooltip } from "antd";
import axios from "axios";
import { useParams } from "react-router";

const backendURL = import.meta.env.VITE_BACKEND_URL;

const IncomeStatementTab = () => {
  const { id: symbol } = useParams();
  const [period, setPeriod] = useState("annual");
  const [headers, setHeaders] = useState([]);
  const [rows, setRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await axios.get(
        `${backendURL}/financial/income-statement/${symbol}/${period}`
      );
      const { headers, items } = res.data.response.data;
      setHeaders(headers);
      setRows(items.map((item) => ({ ...item, key: item.item_id })));
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (symbol) fetchData();
  }, [symbol, period]);

  const columns = [
    {
      title: (
        <Tooltip title="البند">
          <div style={{ whiteSpace: "nowrap" }}>البند</div>
        </Tooltip>
      ),
      dataIndex: "item_name_ar",
      key: "item_name_ar",
      fixed: "left",
      width: 250,
      ellipsis: { showTitle: false },
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ whiteSpace: "nowrap" }}>{text}</div>
        </Tooltip>
      ),
    },
    ...headers.map((h) => ({
      title: (
        <Tooltip title={h}>
          <div style={{ whiteSpace: "nowrap" }}>{h}</div>
        </Tooltip>
      ),
      key: h,
      align: "center",
      dataIndex: ["values", h],
      width: 140,
      ellipsis: true,
      render: (val) => val || "0.00",
    })),
  ];

  return (
    <div className="p-5 bg-white dark:bg-gray-800 shadow-md rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white">
Income Statement        </h2>
        <Radio.Group
          value={period}
          onChange={(e) => setPeriod(e.target.value)}
          optionType="button"
          buttonStyle="solid"
        >
          <Radio.Button value="annual">Annual</Radio.Button>
          <Radio.Button value="half-year">Quarterly</Radio.Button>
        </Radio.Group>
      </div>

      {loading ? (
        <Spin />
      ) : error ? (
        <Alert
          type="error"
          message="خطأ في جلب البيانات"
          description={error.message}
        />
      ) : (
        <div className="w-full overflow-auto">
          <Table
            columns={columns}
            dataSource={rows}
            pagination={false}
            bordered
            scroll={{ x: "max-content" }}
            size="small"
            className="dark:text-white"
          />
        </div>
      )}
    </div>
  );
};

export default IncomeStatementTab;
