import React, { useState, useEffect } from "react";
import { Modal, Checkbox, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { filterCategories } from "../StockScreener/FiltersData";

const FilterModal = ({ visible, onClose, title = "Indicators", onOk, selectedFilters, setSelectedFilters }) => {
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (visible) {
      setSearchTerm("");
    }
  }, [visible]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  return (
    <Modal
      title={<b>{title}</b>}
      open={visible}
      onOk={onOk}
      onCancel={onClose}
      width={1000}
      destroyOnClose
      maskClosable
      style={{ top: 50 }}
      bodyStyle={{ maxHeight: "70vh", overflowY: "auto", paddingRight: "12px" }}
    >
      <Input
        placeholder="Search"
        prefix={<SearchOutlined />}
        className="mb-4"
        onChange={handleSearchChange}
        allowClear
      />

      <div className="space-y-6">
        {filterCategories.map((category) => {
          const filteredOptions =
            searchTerm.trim() === ""
              ? category.filters
              : category.filters.filter((filter) =>
                  filter.toLowerCase().includes(searchTerm)
                );

          return (
            <div key={category.title} className="mb-2">
              <h3 className="font-semibold text-sm mb-2 text-gray-700 border-b pb-1">
                {category.title}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredOptions.map((item) => (
                  <Checkbox
                    key={item}
                    value={item}
                    checked={selectedFilters.includes(item)}
                    onChange={(e) => {
                      const { checked } = e.target;
                      if (checked) {
                        setSelectedFilters((prev) => [...prev, item]);
                      } else {
                        setSelectedFilters((prev) =>
                          prev.filter((f) => f !== item)
                        );
                      }
                    }}
                  >
                    {item}
                  </Checkbox>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default FilterModal;