import React from "react";
import { Table, Checkbox } from "antd";

export default function Performance({ data, selectedRowKeys, onChange }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    { title: "Price", dataIndex: "price" },

    // 1D Change
    {
      title: "1D Change",
      dataIndex: "dayChange",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
    // 1W Change
    {
      title: "1W Change",
      dataIndex: "weekChange",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
    // 1M Change
    {
      title: "1M Change",
      dataIndex: "monthChange",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
    // YTD Change
    {
      title: "YTD Change",
      dataIndex: "ytdChange",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
    // 1Y Change
    {
      title: "1Y Change",
      dataIndex: "yearChange",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
  ];

  return <Table columns={columns} dataSource={data} pagination={false} />;
}
