import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import PropTypes from "prop-types";
import { addInvestment } from "../../redux/portfolio/portfolioSlice";
import ModernSearchBar from "../../components/common/ModernSearchBar";
import { toast } from "react-toastify";
import { X } from "lucide-react";

const backendURL = import.meta.env.VITE_BACKEND_URL || "http://localhost:3000/api";

const AddInvestmentModal = ({ isOpen, onClose, portfolioId, selectedStock: initialSelectedStock }) => {
  const [selectedStock, setSelectedStock] = useState(initialSelectedStock || null);
  
  // Update selectedStock when initialSelectedStock changes
  useEffect(() => {
    if (initialSelectedStock) {
      setSelectedStock(initialSelectedStock);
    }
  }, [initialSelectedStock]);
  
  // Function to fetch historical price data
  const fetchHistoricalPrice = async (symbol, date, index) => {
    if (!symbol || !date || !portfolioId) return;
    
    try {
      setIsLoadingHistoricalPrice(true);
      const token = getAuthToken();
      
      if (!token) {
        console.error('Authentication required to fetch historical price');
        return;
      }
      
      const response = await fetch(
        `${backendURL}/portfolios/${portfolioId}/holdings/close-price?symbol=${symbol}&exchange=TADAWUL&date=${date}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include'
        }
      );
      
      const data = await response.json();
      
      if (data && data.closePrice) {
        // Update the purchase price for the specific transaction using functional update pattern
        // This ensures we're using the latest state and not a stale closure
        setTransactions(prevTransactions => 
          prevTransactions.map((t, i) => 
            i === index ? { ...t, purchasePrice: data.closePrice } : t
          )
        );
      }
    } catch (error) {
      console.error('Error fetching historical price:', error);
    } finally {
      setIsLoadingHistoricalPrice(false);
    }
  };
  const [transactions, setTransactions] = useState([
    { quantity: 0, purchaseDate: "", purchasePrice: "" },
  ]);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingHistoricalPrice, setIsLoadingHistoricalPrice] = useState(false);
  const dispatch = useDispatch();

  const getAuthToken = () => {
    const localToken = localStorage.getItem("token");
    if (localToken) {
      return localToken;
    }
    return null;
  };

  const handleSave = async () => {
    if (
      selectedStock &&
      transactions.every(
        (t) => t.quantity > 0 && t.purchaseDate && t.purchasePrice
      )
    ) {
      setIsLoading(true);
      setError(null);

      // Show loading toast
      const toastId = toast.loading("Adding investment...");

      try {
        const token = getAuthToken();

        if (!token) {
          toast.update(toastId, {
            render: "Authentication required. Please log in.",
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          setError("Authentication required. Please log in.");
          setIsLoading(false);
          return;
        }

        // Prepare holdings data for API
        const holdingsData = transactions.map((transaction) => ({
          symbol: selectedStock.symbol,
          quantity: transaction.quantity.toString(),
          purchase_price: transaction.purchasePrice.toString(),
          purchase_date: transaction.purchaseDate,
        }));

        // Call the API endpoint
        const response = await fetch(
          `${backendURL}/portfolios/${portfolioId}/holdings/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(holdingsData),
            credentials: "include",
          }
        );

        const data = await response.json();

        if (data.success) {
          toast.update(toastId, {
            render: `${selectedStock.name} added to portfolio successfully!`,
            type: "success",
            isLoading: false,
            autoClose: 2000,
          });

          // Also update Redux store
      transactions.forEach((transaction) => {
        const investment = {
          symbol: selectedStock.symbol,
          name: selectedStock.name,
          quantity: parseInt(transaction.quantity),
          purchaseDate: transaction.purchaseDate,
          purchasePrice: parseFloat(transaction.purchasePrice),
          currentPrice: selectedStock.price || 0,
          change: selectedStock.change || 0,
        };
        dispatch(addInvestment({ portfolioId, investment }));
      });

      handleClose();
        } else {
          const errorMessage = data.message || "Unknown error";
          toast.update(toastId, {
            render: `Failed to add ${selectedStock.name}: ${errorMessage}`,
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          setError(`Failed to add holdings: ${errorMessage}`);
        }
      } catch (err) {
        console.error("Error adding holdings:", err);
        toast.update(toastId, {
          render: `Error: ${err.message || "Failed to connect to server"}`,
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
        setError("Error adding holdings: " + err.message);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Check what fields are missing
      let missingFields = [];

      if (!selectedStock) {
        missingFields.push("stock selection");
      }

      transactions.forEach((transaction, index) => {
        if (!transaction.quantity || transaction.quantity <= 0) {
          missingFields.push(`quantity (transaction ${index + 1})`);
        }
        if (!transaction.purchaseDate) {
          missingFields.push(`purchase date (transaction ${index + 1})`);
        }
        if (!transaction.purchasePrice) {
          missingFields.push(`purchase price (transaction ${index + 1})`);
        }
      });

      const errorMessage = `Please complete all required fields: ${missingFields.join(
        ", "
      )}`;
      toast.error(errorMessage);
      setError(errorMessage);
    }
  };

  const handleSaveAndAddAnother = async () => {
    if (
      selectedStock &&
      transactions.every(
        (t) => t.quantity > 0 && t.purchaseDate && t.purchasePrice
      )
    ) {
      setIsLoading(true);
      setError(null);

      // Show loading toast
      const toastId = toast.loading("Adding investment...");

      try {
        const token = getAuthToken();

        if (!token) {
          toast.update(toastId, {
            render: "Authentication required. Please log in.",
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          setError("Authentication required. Please log in.");
          setIsLoading(false);
          return;
        }

        // Prepare holdings data for API
        const holdingsData = transactions.map((transaction) => ({
          symbol: selectedStock.symbol,
          quantity: transaction.quantity.toString(),
          purchase_price: transaction.purchasePrice.toString(),
          purchase_date: transaction.purchaseDate,
        }));

        // Call the API endpoint
        const response = await fetch(
          `${backendURL}/portfolios/${portfolioId}/holdings/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(holdingsData),
            credentials: "include",
          }
        );

        const data = await response.json();

        if (data.success) {
          toast.update(toastId, {
            render: `${selectedStock.name} added to portfolio successfully!`,
            type: "success",
            isLoading: false,
            autoClose: 2000,
          });

          // Also update Redux store
      transactions.forEach((transaction) => {
        const investment = {
          symbol: selectedStock.symbol,
          name: selectedStock.name,
          quantity: parseInt(transaction.quantity),
          purchaseDate: transaction.purchaseDate,
          purchasePrice: parseFloat(transaction.purchasePrice),
          currentPrice: selectedStock.price || 0,
          change: selectedStock.change || 0,
        };
        dispatch(addInvestment({ portfolioId, investment }));
      });

          // Reset form for adding another
      setSelectedStock(null);
          setTransactions([
            { quantity: 0, purchaseDate: "", purchasePrice: "" },
          ]);
          setError(null);

          // Show a message prompting to add another
          toast.info("You can now add another investment", {
            autoClose: 3000,
          });
        } else {
          const errorMessage = data.message || "Unknown error";
          toast.update(toastId, {
            render: `Failed to add ${selectedStock.name}: ${errorMessage}`,
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
          setError(`Failed to add holdings: ${errorMessage}`);
        }
      } catch (err) {
        console.error("Error adding holdings:", err);
        toast.update(toastId, {
          render: `Error: ${err.message || "Failed to connect to server"}`,
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
        setError("Error adding holdings: " + err.message);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Check what fields are missing
      let missingFields = [];

      if (!selectedStock) {
        missingFields.push("stock selection");
      }

      transactions.forEach((transaction, index) => {
        if (!transaction.quantity || transaction.quantity <= 0) {
          missingFields.push(`quantity (transaction ${index + 1})`);
        }
        if (!transaction.purchaseDate) {
          missingFields.push(`purchase date (transaction ${index + 1})`);
        }
        if (!transaction.purchasePrice) {
          missingFields.push(`purchase price (transaction ${index + 1})`);
        }
      });

      const errorMessage = `Please complete all required fields: ${missingFields.join(
        ", "
      )}`;
      toast.error(errorMessage);
      setError(errorMessage);
    }
  };

  const handleAddTransaction = () => {
    setTransactions([
      ...transactions,
      { quantity: 0, purchaseDate: "", purchasePrice: "" },
    ]);
  };

  const handleDeleteTransaction = (indexToDelete) => {
    const updated = transactions.filter((_, index) => index !== indexToDelete);
    setTransactions(updated);
  };

  const handleClose = () => {
    setSelectedStock(null);
    setTransactions([{ quantity: 0, purchaseDate: "", purchasePrice: "" }]);
    setError(null);
    onClose();
  };

  return (
    <React.Fragment>
      {isOpen && (
        <div
          className="fixed inset-0 flex items-center justify-center p-4 z-50"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md border border-gray-100 dark:border-gray-700">
        <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                  Add to Portfolio
                </h2>
                <button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
          
          {!selectedStock ? (
            <div className="mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    Type an investment name or symbol
                  </p>
                  <ModernSearchBar onSelectStock={setSelectedStock} portfolioId={portfolioId} />
            </div>
          ) : (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {selectedStock.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedStock.symbol}
                      </p>
                </div>
                <div className="text-right">
                      <p className="font-medium dark:text-white">
                        {(selectedStock.price !== undefined || selectedStock.currentPrice !== undefined) 
                          ? `${typeof (selectedStock.price || selectedStock.currentPrice) === 'string' 
                              ? parseFloat(selectedStock.price || selectedStock.currentPrice).toFixed(2) 
                              : (selectedStock.price || selectedStock.currentPrice).toFixed(2)} SAR`
                          : "N/A"}
                        <span
                          className={`ml-1 ${
                            // Updated to use green for positive, red for negative
                            parseFloat(selectedStock.day_gain_percent || selectedStock.change || selectedStock.day_gain || 0) >= 0
                              ? "text-green-500"
                              : "text-red-500"
                          }`}
                        >
                      {(selectedStock.day_gain_percent !== undefined || selectedStock.change !== undefined || selectedStock.day_gain !== undefined)
                            ? `${
                                parseFloat(selectedStock.day_gain_percent || selectedStock.change || selectedStock.day_gain || 0) >= 0 ? "▲" : "▼"
                              } ${Math.abs(parseFloat(selectedStock.day_gain_percent || selectedStock.change || selectedStock.day_gain || 0)).toFixed(2)}%`
                            : ""}
                    </span>
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {transactions.map((transaction, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-3 gap-3 relative"
                      >
                    <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Quantity
                          </label>
                          <div className="flex border rounded-md dark:border-gray-600">
                        <button 
                              className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                              onClick={() =>
                                setTransactions(
                            transactions.map((t, i) =>
                                    i === index
                                      ? {
                                          ...t,
                                          quantity: Math.max(0, t.quantity - 1),
                                        }
                                      : t
                                  )
                                )
                              }
                        >
                          -
                        </button>
                        <input
                          type="number"
                          value={transaction.quantity}
                          onChange={(e) =>
                            setTransactions(
                              transactions.map((t, i) =>
                                    i === index
                                      ? { ...t, quantity: e.target.value }
                                      : t
                              )
                            )
                          }
                              className="w-full px-3 py-2 text-center border-0 focus:ring-0 dark:bg-gray-700 dark:text-white"
                        />
                        <button 
                              className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                              onClick={() =>
                                setTransactions(
                            transactions.map((t, i) =>
                                    i === index
                                      ? {
                                          ...t,
                                          quantity:
                                            (parseInt(t.quantity) || 0) + 1,
                                        }
                                      : t
                                  )
                                )
                              }
                        >
                          +
                        </button>
                      </div>
                    </div>
                    
                    <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Purchase date
                          </label>
                      <input
                        type="date"
                        value={transaction.purchaseDate}
                        onChange={(e) => {
                          const newDate = e.target.value;
                          // Use functional update pattern to ensure we're using the latest state
                          setTransactions(prevTransactions =>
                            prevTransactions.map((t, i) =>
                                  i === index
                                    ? { ...t, purchaseDate: newDate }
                                    : t
                            )
                          );
                          
                          // Fetch historical price when date is selected
                          if (newDate && selectedStock?.symbol) {
                            fetchHistoricalPrice(selectedStock.symbol, newDate, index);
                          }
                        }}
                            className="w-full px-3 py-2 border rounded-md text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                    
                    <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Purchase price
                          </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={transaction.purchasePrice}
                          onChange={(e) =>
                            setTransactions(
                              transactions.map((t, i) =>
                                    i === index
                                      ? { ...t, purchasePrice: e.target.value }
                                      : t
                              )
                            )
                          }
                          className="w-full px-3 py-2 border rounded-md text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                          placeholder={isLoadingHistoricalPrice ? "Loading..." : "Enter price"}
                        />
                        {isLoadingHistoricalPrice && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {transactions.length > 1 && (
                      <button
                        onClick={() => handleDeleteTransaction(index)}
                            className="absolute -right-1 -top-1 bg-gray-200 dark:bg-gray-600 text-black dark:text-white w-5 h-5 flex items-center justify-center text-xs hover:bg-gray-400 dark:hover:bg-gray-500"
                        title="Delete"
                      >
                        X
                      </button>
                    )}
                  </div>
                ))}
              </div>

              <button
                onClick={handleAddTransaction}
                    className="mt-4 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium flex items-center"
              >
                    <span className="mr-1">+</span> More purchases of{" "}
                    {selectedStock.symbol}
              </button>

                  {error && (
                    <p className="mt-4 text-red-500 text-sm">{error}</p>
                  )}
            </div>
          )}
        </div>

            <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end space-x-3 rounded-b-lg">
          <button
            onClick={handleClose}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 font-medium bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500"
                disabled={isLoading}
          >
            Cancel
          </button>

              {selectedStock && (
                <>
          <button
            onClick={handleSaveAndAddAnother}
                    className="px-4 py-2 text-blue-600 dark:text-blue-400 font-medium bg-blue-100 dark:bg-blue-900/30 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50"
                    disabled={isLoading}
          >
                    Save & Add Another
          </button>

          <button
            onClick={handleSave}
                    className="px-4 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 flex items-center justify-center"
                    disabled={isLoading}
          >
                    {isLoading ? (
                      <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></span>
                    ) : null}
            Save
          </button>
                </>
              )}
        </div>
      </div>
    </div>
      )}
    </React.Fragment>
  );
};

AddInvestmentModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  portfolioId: PropTypes.string.isRequired,
  selectedStock: PropTypes.shape({
    symbol: PropTypes.string,
    name: PropTypes.string,
    currentPrice: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    price: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    day_gain_percent: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    change: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
  })
};

export default AddInvestmentModal;
