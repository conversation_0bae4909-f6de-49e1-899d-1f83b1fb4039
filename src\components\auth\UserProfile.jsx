import { useSelector } from "react-redux";

const UserProfile = () => {
  const { userInfo } = useSelector((state) => state.user);

  // Use userName property from userInfo (as used in AppHeader)
  const username = userInfo?.userName || "User";
  const initial = username.charAt(0).toUpperCase();

  return (
    <div className="profile-container p-4 bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      <figure className="text-4xl font-bold mb-4">{initial}</figure>
      <span>
        Welcome <strong>{username}!</strong> You can view this page because you're logged in.
      </span>
    </div>
  );
};

export default UserProfile;
