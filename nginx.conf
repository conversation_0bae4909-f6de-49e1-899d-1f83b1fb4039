server {
    listen 80;
    listen [::]:80;

    root /usr/share/nginx/html;
    index index.html index.htm;

    # Frontend SPA routing - serves index.html for non-API requests
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Proxy all API requests to the backend service
    # The trailing slash on 'proxy_pass http://backend:3000/;' is important
    # It ensures that /api/auth/register becomes http://backend:3000/api/auth/register
    location /api/ {
        proxy_pass http://backend:3000/api/; # <--- CRITICAL CHANGE HERE: added /api/ to proxy_pass
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        # For better error reporting and to ensure full headers are passed
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Optional: If you use WebSockets (e.g., for live data, chat), you might need this specific block
    # location /socket.io/ { # Adjust path to match your WebSocket endpoint if not /socket.io/
    #     proxy_pass http://backend:3000/socket.io/; # Ensure this matches the internal backend path
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "Upgrade";
    #     proxy_set_header Host $host;
    # }
}
