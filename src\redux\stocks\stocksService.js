import axios from "axios";

const API_KEY = import.meta.env.VITE_STOCKS_API_KEY;
const BASE_URL = import.meta.env.VITE_STOCKS_BASE_URL;
const backendURL = import.meta.env.VITE_BACKEND_URL;

export const fetchStocksList = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/stocks`, {
      params: {
        apikey: API_KEY,
        exchange: "Tadawul",
      },
    });
    if (!response.data || !response.data.data) {
      throw new Error("Invalid API response format");
    }
    return response.data.data.map((stock) => ({
      symbol: stock.symbol,
      name: stock.name,
      currency: stock.currency,
      exchange: stock.exchange,
      country: stock.country,
      type: stock.type,
      marketCap: stock.market_cap,
      enterpriseValue: stock.enterprise_value,
      peRatio: stock.pe_ratio,
      forwardPE: stock.forward_pe,
      psRatio: stock.ps_ratio,
      pbRatio: stock.pb_ratio,
      pfcfRatio: stock.pfcf_ratio,
      industry: stock.industry,
      sector: stock.sector,
      stockPrice: stock.stock_price,
      dividendYield: stock.dividend_yield,
      dividendPerShare: stock.dividend_per_share,
      lastDividend: stock.last_dividend,
      priceChange1D: stock.price_change_1d,
      priceChange1W: stock.price_change_1w,
      priceChange1M: stock.price_change_1m,
      totalReturn1Y: stock.total_return_1y,
      revenue: stock.revenue,
      netIncome: stock.net_income,
      eps: stock.eps,
      assets: stock.assets,
      liabilities: stock.liabilities,
      roe: stock.return_on_equity,
      roa: stock.return_on_assets,
    }));
  } catch (error) {
    console.error("Error fetching stock data:", error.response?.data || error.message);
    throw new Error(`Failed to fetch stock data: ${error.message}`);
  }
};

export const fetchStockQuotesBySymbols = async (symbols) => {
  if (!symbols || symbols.length === 0) return [];
  try {
    const response = await axios.get(`${BASE_URL}/quote`, {
      params: {
        symbol: symbols.join(","),
        apikey: API_KEY,
      },
    });
    const quotes = Array.isArray(response.data) ? response.data : [response.data];
    return quotes.map((q) => ({
      symbol: q.symbol || "N/A",
      stockPrice: q.close || 0,
      priceChange1D: q.percent_change || 0,
      dividendYield: q.dividend_yield || 0,
      peRatio: q.pe_ratio || 0,
      high: q.fifty_two_week?.high || 0,
      low: q.fifty_two_week?.low || 0,
      marketCap: q.market_cap || 0,
    }));
  } catch (error) {
    console.error("Error fetching stock quotes:", error);
    throw new Error("Failed to fetch stock quotes");
  }
};

export const fetchTimeSeriesData = async (symbol, interval = "1day", outputsize = 30) => {
  try {
    const response = await axios.get(`${BASE_URL}/time_series`, {
      params: { symbol, interval, outputsize, apikey: API_KEY },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching time series data:", error);
    throw error;
  }
};

export const fetchTasiStocks = async () => {
  const res = await axios.get(`${backendURL}/home/<USER>/tasi`);
  return res.data.data;
};

export const fetchNumoStocks = async () => {
  const res = await axios.get(`${backendURL}/home/<USER>/numo`);
  return res.data.data;
};

export const fetchAllMarketStocks = async () => {
  const res = await axios.get(`${backendURL}/home/<USER>/all-market`);
  return res.data.data;
};

/**
 * Fetch the header details for a stock via backend search endpoint.
 */
export const fetchStockHeader = async (symbol) => {
  try {
    const response = await axios.get(`${backendURL}/home/<USER>
      params: { q: symbol },
    });
    if (response.data.success && response.data.data.length > 0) {
      return response.data.data[0];
    } else {
      throw new Error("No matching stock found");
    }
  } catch (err) {
    console.error("Error fetching stock header:", err.response?.data || err.message);
    throw new Error("Failed to fetch stock header");
  }
};

// New: fetch all industries
export const fetchIndustries = async () => {
  const res = await axios.get(`${backendURL}/industries/get-all-industries`);
  return res.data.response.data.industries;
};

// New: fetch stocks by selected industry
export const fetchStocksByIndustry = async (industry) => {
  const res = await axios.get(
    `${backendURL}/industries/search-by-industries`,
    { params: { industry } }
  );
  return res.data.response.data;
};

