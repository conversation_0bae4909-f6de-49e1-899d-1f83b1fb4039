
// // const Store = () => {
// //   return (
// //     <div>Store</div>
// //   )
// // }

// // export default Store





// import React from "react";
// import { Card, CardContent } from "./react/card";
// import { Button } from "../components/ui/button";

// const categories = [
//   {
//     title: "Our best-sellers",
//     books: [
//       "An Immense World",
//       "Atomic Habits",
//       "Maid",
//       "The Family Upstairs",
//       "Colorless Tsukuru <PERSON>..."
//     ]
//   },
//   {
//     title: "Discover the world as you've never seen it before",
//     books: ["An Immense World"]
//   },
//   {
//     title: "Popular in Hindi",
//     books: ["आनंदमठ", "हिटलर चित्र" /* more titles */]
//   },
//   {
//     title: "Now & Hot Audiobooks",
//     books: ["The Whistle", "The Night of the Moth", "Meet Cute", "You Could Make This Place Beautiful"]
//   },
//   {
//     title: "Popular Titles for Less",
//     books: ["रोमिओ और जूलियट", "MI<PERSON><PERSON> INDIANA", "हिटलर चित्र"]
//   },
//   {
//     title: "A Tale of Magic and Intrigue",
//     books: ["The Familiar"]
//   },
//   {
//     title: "Better life, better you",
//     books: ["Audiobook Optimism", "Grow the F*ck Up", "The Subtle Cure"]
//   },
//   {
//     title: "Pre-order these titles now",
//     books: ["The Wizard and Me", "The Light of Day", "Final Chance"]
//   },
//   {
//     title: "Audiobooks under ₹400",
//     books: ["The Midnight Prince", "Love from A to Z"]
//   },
//   {
//     title: "Dictionaries and tools to help with language learning",
//     books: ["English Conversation", "Let's Learn Hindi", "Portuguese", "Chinese Characters"]
//   },
//   {
//     title: "Read the film or series",
//     books: ["Gone Girl", "Fifty Shades of Grey", "Origin"]
//   },
//   {
//     title: "Arundhati Roy's incredible follow-up",
//     books: ["Ministry of Utmost Happiness"]
//   },
//   {
//     title: "eBooks in Indian Languages",
//     books: ["गोदान", "গীতাঞ্জলি"]
//   },
//   {
//     title: "The Harry Potter series",
//     books: [
//       "Harry Potter and the Philosopher's Stone",
//       "Harry Potter and the Chamber of Secrets",
//       "Harry Potter and the Prisoner of Azkaban"
//     ]
//   }
// ];

// const BookstoreHomePage = () => {
//   return (
//     <div className="p-6 space-y-12">
//       <header className="bg-emerald-100 p-6 rounded-2xl text-center">
//         <h1 className="text-3xl font-bold mb-2">Explore what everyone is reading today</h1>
//         <Button className="mt-2">Browse Now</Button>
//       </header>

//       {categories.map((section, idx) => (
//         <div key={idx}>
//           <h2 className="text-2xl font-semibold mb-4">{section.title}</h2>
//           <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
//             {section.books.map((title, index) => (
//               <Card key={index} className="shadow-md hover:shadow-lg transition">
//                 <CardContent className="p-4">
//                   <div className="h-32 bg-gray-100 mb-2 rounded-lg" />
//                   <h3 className="text-sm font-medium">{title}</h3>
//                 </CardContent>
//               </Card>
//             ))}
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default BookstoreHomePage;









const categories = [
  {
    title: "Our best-sellers",
    books: [
      "Inveasting 101",
      "Guide to Tadawul Market",
      "Basics Of Markets",
      "Fundamental Analysis for Investors",
      "Technical Analysis for Investors",
      "Stock Financial Market Laws and Regulations",
      "The Secret Science of Price and Volume",
      "Candle Stick Charting",
      "The Art of Execution",
      "Oppurtunity Investing",
      "Ahead of the Market"
    ]
  },
  // {
  //   title: "Discover the world as you've never seen it before",
  //   books: ["An Immense World"]
  // },
  // {
  //   title: "Popular in Hindi",
  //   books: ["आनंदमठ", "हिटलर चित्र" /* more titles */]
  // },
  // {
  //   title: "Now & Hot Audiobooks",
  //   books: ["The Whistle", "The Night of the Moth", "Meet Cute", "You Could Make This Place Beautiful"]
  // },
  // {
  //   title: "Popular Titles for Less",
  //   books: ["रोमिओ और जूलियट", "MISSION INDIANA", "हिटलर चित्र"]
  // },
  // {
  //   title: "A Tale of Magic and Intrigue",
  //   books: ["The Familiar"]
  // },
  // {
  //   title: "Better life, better you",
  //   books: ["Audiobook Optimism", "Grow the F*ck Up", "The Subtle Cure"]
  // },
  // {
  //   title: "Pre-order these titles now",
  //   books: ["The Wizard and Me", "The Light of Day", "Final Chance"]
  // },
  // {
  //   title: "Audiobooks under ₹400",
  //   books: ["The Midnight Prince", "Love from A to Z"]
  // },
  // {
  //   title: "Dictionaries and tools to help with language learning",
  //   books: ["English Conversation", "Let's Learn Hindi", "Portuguese", "Chinese Characters"]
  // },
  // {
  //   title: "Read the film or series",
  //   books: ["Gone Girl", "Fifty Shades of Grey", "Origin"]
  // },
  // {
  //   title: "Arundhati Roy's incredible follow-up",
  //   books: ["Ministry of Utmost Happiness"]
  // },
  // {
  //   title: "eBooks in Indian Languages",
  //   books: ["गोदान", "গীতাঞ্জলি"]
  // },
  // {
  //   title: "The Harry Potter series",
  //   books: [
  //     "Harry Potter and the Philosopher's Stone",
  //     "Harry Potter and the Chamber of Secrets",
  //     "Harry Potter and the Prisoner of Azkaban"
  //   ]
  // }
];

const BookstoreHomePage = () => {
  return (
    <div className="p-6 space-y-12">
      <header className="bg-emerald-100 p-6 rounded-2xl text-center">
        <h1 className="text-3xl font-bold mb-2">Explore what everyone is reading today</h1>
        <button className="bg-emerald-600 text-white px-4 py-2 rounded mt-2 hover:bg-emerald-700">Browse Now</button>
      </header>

      {categories.map((section, idx) => (
        <div key={idx}>
          <h2 className="text-2xl font-semibold mb-4">{section.title}</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            {section.books.map((title, index) => (
              <div key={index} className="bg-white border rounded-xl shadow-md hover:shadow-lg transition p-4">
                <div className="h-32 bg-gray-100 mb-2 rounded-lg" />
                <h3 className="text-sm font-medium">{title}</h3>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default BookstoreHomePage;

