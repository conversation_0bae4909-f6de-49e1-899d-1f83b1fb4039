import { Outlet, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "antd";

import { fetchStockHeaderBySymbol } from "../../redux/stocks/stocksActions";
import Header from "./Header";
import TabNav from "./TabNav";
import WatchlistSelectModal from "../Watchlist/WatchlistSelectModal";
import { addStock, loadWatchlists } from "../../redux/watchlist/watchlistSlice";

export default function StockDetailsLayout() {
  const { id } = useParams();
  const dispatch = useDispatch();
  const header = useSelector((state) => state.stocks.header);
  const watchlists = useSelector((state) => state.watchlist.items);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(fetchStockHeaderBySymbol(id));
    }
    dispatch(loadWatchlists());
  }, [id, dispatch]);

  const openModal = () => setModalVisible(true);
  const closeModal = () => setModalVisible(false);

  const handleConfirm = (watchlistId) => {
    dispatch(addStock({ watchlistId, symbol: id }));
    closeModal();
  };

  return (
    <div className="stock-details-layout">
      <Header header={header} />

      <TabNav />

      <div className="tab-content-area p-4">
        <Button type="primary" onClick={openModal}>
          Add to Watchlist
        </Button>
        <Outlet />
      </div>

      <WatchlistSelectModal
        visible={modalVisible}
        onClose={closeModal}
        onConfirm={handleConfirm}
      />
    </div>
  );
}
