import { Link, useParams, useLocation } from 'react-router-dom';

const TabNavigation = () => {
  const { id } = useParams();
  const location = useLocation();

  const tabs = [
    { path: 'overview', label: 'Overview' },
    { path: 'balancesheet', label: 'Balance Sheet' },
    { path: 'incomestatement', label: 'Income Statement' },
    { path: 'cashflow', label: 'Cash Flow' },
    { path: 'ratio', label: 'Ratio' },
    { path: 'chart', label: 'Chart' },
    { path: 'profile', label: 'Profile' },
  ];

  return (
   
    <nav className="stock-tabs text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
      {tabs.map((tab) => (
        <Link
          key={tab.path}
          to={`/stocks/${id}/${tab.path}`}
          className={`inline-block w-full p-4 border-r border-gray-200 dark:border-gray-700 rounded-s-lg cursor-pointer ${
            location.pathname.includes(tab.path)
              ? "bg-gray-100 underline underline-offset-9 text-black font-bold dark:text-white dark:bg-gray-800"
              : "hover:bg-gray-200 dark:hover:bg-gray-900"
          }`}
          >
      <span>{tab.label}</span>
        </Link>
        
      ))}
     
    </nav>
  );
};

export default TabNavigation;
