import axios from 'axios';

const API = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL + '/watchlist',
  withCredentials: true,
});

export const fetchWatchlists = () => API.get('/');

export const createWatchlist = (name) => API.post('/create', { watchlist_name: name });

export const renameWatchlist = (id, newName) => API.put(`/rename/${id}`, { new_name: newName });

export const deleteWatchlist = (id) => API.delete(`/delete/${id}`);

export const addStockToWatchlist = (watchlistId, symbol) =>
  API.post(`/stock/add/${watchlistId}`, { stock_symbol: symbol });

export const removeStockFromWatchlist = (watchlistId, symbol) =>
  API.delete(`/stock/remove`, { data: { watchlist_id: watchlistId, stock_symbol: symbol } });



// export const fetchWatchlistByName = (name) =>
//   API.get(`/by-name/${encodeURIComponent(name)}`);