import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  data: [],
};

const chartSlice = createSlice({
  name: 'chart',
  initialState,
  reducers: {
    setChartData(state, action) {
      console.log('Setting chart data:', action.payload);
      state.data = action.payload;
    },
    updateChartData(state, action) {
      console.log('Updating chart data:', action.payload);
      const newPoint = action.payload;
      const lastPoint = state.data[state.data.length - 1];

      if (!lastPoint) {
        console.log('Adding first candle:', newPoint);
        state.data.push(newPoint);
      } else if (newPoint.time === lastPoint.time) {
        // Update current candle
        console.log('Updating current candle:', { ...lastPoint, ...newPoint });
        state.data[state.data.length - 1] = {
          time: newPoint.time,
          open: lastPoint.open, // Preserve original open
          high: Math.max(lastPoint.high, newPoint.high),
          low: Math.min(lastPoint.low, newPoint.low),
          close: newPoint.close,
        };
      } else if (newPoint.time > lastPoint.time) {
        console.log('Adding new candle:', newPoint);        state.data.push(newPoint);
      } else {
        console.warn('Outdated data ignored:', newPoint);
      }
    },
  },
});

export const { setChartData, updateChartData } = chartSlice.actions;
export default chartSlice.reducer;