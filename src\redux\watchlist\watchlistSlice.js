import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import * as service from './watchlistService';

// Thunks for watchlist CRUD
export const loadWatchlists = createAsyncThunk(
  'watchlist/loadWatchlists',
  async (_, { rejectWithValue }) => {
    try {
      const res = await service.fetchWatchlists();
      return res.data.watchlists;
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);
export const createNewWatchlist = createAsyncThunk(
  'watchlist/create',
  async (name, { rejectWithValue }) => {
    try {
      const res = await service.createWatchlist(name);
      return res.data.watchlist;
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);
export const renameExistingWatchlist = createAsyncThunk(
  'watchlist/rename',
  async ({ id, newName }, { rejectWithValue }) => {
    try {
      const res = await service.renameWatchlist(id, newName);
      return res.data.watchlist;
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);
export const deleteExistingWatchlist = createAsyncThunk(
  'watchlist/delete',
  async (id, { rejectWithValue }) => {
    try {
      await service.deleteWatchlist(id);
      return id;
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);
export const addStock = createAsyncThunk(
  'watchlist/addStock',
  async ({ watchlistId, symbol }, { rejectWithValue }) => {
    try {
      const res = await service.addStockToWatchlist(watchlistId, symbol);
      return { watchlistId, stock: res.data.stock };
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);
export const removeStock = createAsyncThunk(
  'watchlist/removeStock',
  async ({ watchlistId, symbol }, { rejectWithValue }) => {
    try {
      await service.removeStockFromWatchlist(watchlistId, symbol);
      return { watchlistId, symbol };
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || err.message);
    }
  }
);

// Slice
const watchlistSlice = createSlice({
  name: 'watchlist',
  initialState: {
    items: [],
    loading: false,
    error: null,
    customTabs: [],   // ← new state for your custom tabs
  },
  reducers: {
    addCustomTab: (state, action) => {
      state.customTabs.push({
        id: action.payload.id,
        name: action.payload.name,
        filters: [],
      });
    },
    setCustomTabFilters: (state, action) => {
      const tab = state.customTabs.find(t => t.id === action.payload.id);
      if (tab) {
        tab.filters = action.payload.filters;
      }
    },
     deleteCustomTab: (state, action) => {
      state.customTabs = state.customTabs.filter(t => t.id !== action.payload);
    },
    clearAllCustomTabs: (state) => {
      state.customTabs = [];
    },
    
    
  },

  
  extraReducers: builder => {
    builder
      .addCase(loadWatchlists.pending, state => { state.loading = true; })
      .addCase(loadWatchlists.fulfilled, (state, { payload }) => {
        state.loading = false;
        state.items = payload.map(w => ({ ...w, stocks: w.stocks || [] }));
      })
      .addCase(loadWatchlists.rejected, (state, { payload }) => {
        state.loading = false;
        state.error = payload;
      })
      .addCase(createNewWatchlist.fulfilled, (state, { payload }) => {
        state.items.push({ id: payload.id, name: payload.name, stocks: [] });
      })
      .addCase(renameExistingWatchlist.fulfilled, (state, { payload }) => {
        const idx = state.items.findIndex(w => w.id === payload.id);
        if (idx >= 0) state.items[idx].name = payload.name;
      })
      .addCase(deleteExistingWatchlist.fulfilled, (state, { payload }) => {
        state.items = state.items.filter(w => w.id !== payload);
      })
      .addCase(addStock.fulfilled, (state, { payload }) => {
        const wl = state.items.find(w => w.id === payload.watchlistId);
        if (wl) wl.stocks = wl.stocks || [];
        if (wl && !wl.stocks.includes(payload.stock.stock_symbol)) {
          wl.stocks.push(payload.stock.stock_symbol);
        }
      })
      .addCase(removeStock.fulfilled, (state, { payload }) => {
        const wl = state.items.find(w => w.id === payload.watchlistId);
        if (wl) {
          wl.stocks = (wl.stocks || []).filter(s => s !== payload.symbol);
        }
      });
  }
});

export const { addCustomTab, setCustomTabFilters, deleteCustomTab, clearAllCustomTabs } = watchlistSlice.actions;
export default watchlistSlice.reducer;
