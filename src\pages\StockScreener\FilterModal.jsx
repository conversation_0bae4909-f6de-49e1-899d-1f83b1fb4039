import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { addFilter, clearFilters } from '../../redux/filter/filterSlice';
import { filterCategories } from './FiltersData';

const FilterModal = ({
  visible,
  onClose,
  title = "Select screener filters (79 total)", // 🔁 Default title
}) => {
  const dispatch = useDispatch();
  const selectedFilters = useSelector((state) => state.filters.selectedFilters);
  const [localSelected, setLocalSelected] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (visible) {
      setLocalSelected(selectedFilters);
    }
  }, [visible, selectedFilters]);

  const handleOk = () => {
    dispatch(clearFilters());
    localSelected.forEach((f) => dispatch(addFilter(f)));
    onClose();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  return (
    <Modal
      title={<b>{title}</b>} // ✅ Dynamic title
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      width={1000}
      destroyOnClose
      maskClosable={true}
      style={{ top: 50 }}
styles={{
        body: {
          maxHeight: "70vh",
          overflowY: "auto",
          paddingRight: "12px",
        },
      }}    >
      <Input
        placeholder="Search"
        prefix={<SearchOutlined />}
        className="mb-4"
        onChange={handleSearchChange}
        allowClear
      />

      <div className="space-y-6">
        {filterCategories.map((category) => {
          const filteredOptions =
            searchTerm.trim() === ''
              ? category.filters
              : category.filters.filter(
                  (filter) => filter.toLowerCase().includes(searchTerm)
                );

          return (
            <div key={category.title} className="mb-2">
              <h3 className="font-semibold text-sm mb-2 text-gray-700 border-b pb-1">
                {category.title}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredOptions.map((item) => (
                  <Checkbox
                    key={item}
                    value={item}
                    checked={localSelected.includes(item)}
                    onChange={(e) => {
                      const { checked } = e.target;
                      if (checked) {
                        setLocalSelected((prev) => [...prev, item]);
                      } else {
                        setLocalSelected((prev) =>
                          prev.filter((f) => f !== item)
                        );
                      }
                    }}
                  >
                    {item}
                  </Checkbox>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default FilterModal;
