import Applayout from "./components/layouts/Applayout.jsx";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { fetchStocks } from "./redux/stocks/stocksActions";
import {fetchIndexSummary} from "./redux/index/indexSlice";
const App = () => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(fetchStocks());
    console.log("Dispatching fetchIndexSummary...");
  // dispatch(fetchIndexSummary());
  console.log("Dispatched fetchIndexSummary.");
  }, [dispatch]);

  return <Applayout />;
};

export default App;
