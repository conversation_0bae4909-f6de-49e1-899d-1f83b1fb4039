import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const API_KEY = import.meta.env.VITE_STOCKS_API_KEY;
const BASE_URL = import.meta.env.VITE_STOCKS_BASE_URL;

export const fetchIndexSummary = createAsyncThunk(
  "index/fetchIndexSummary",
  async (symbol = "TASI",interval = "1day", outputsize = 30, { rejectWithValue }) => {
    console.log("Fetching index summary...");
    console.log("API URL:", BASE_URL);
    console.log("Params:", { symbol, interval, outputsize, apikey: API_KEY });

    try {
      const response = await axios.get(`${BASE_URL}/time_series`, {
        params: {
          symbol,
          interval,
          outputsize,
          apikey: API_KEY,
        },
      });
      console.log("Index Summary Response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching index summary:", error.message);
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  summary: {
    tasi: { value: 0, change: 0, date: null },
    nomu: { value: 0, change: 0, date: null },
  },
  topGainers: [],
  topLosers: [],
  loading: false,
  error: null,
};

const indexSlice = createSlice({
  name: "index",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchIndexSummary.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchIndexSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.summary = action.payload.summary;
        state.topGainers = action.payload.topGainers;
        state.topLosers = action.payload.topLosers;
      })
      .addCase(fetchIndexSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = indexSlice.actions;
export default indexSlice.reducer;