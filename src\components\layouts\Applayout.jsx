import { useState } from "react";
import { Outlet } from "react-router-dom";
import AppHeader from "./AppHeader";
import AppLeftSideBar from "./AppLeftSideBar";
import AppRightSideBar from "./AppRightSideBar";
import AppFooter from "./AppFooter";
import AppAbout from "./AppAbout";

const AppLayout = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <AppHeader />

      {/* Main section: sidebar + content */}
      <div className="flex flex-grow">
        <AppLeftSideBar isCollapsed={isSidebarCollapsed} toggleSidebar={toggleSidebar} />

        {/* Main content area */}
        <div className="flex-grow p-4 overflow-y-auto bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
          <Outlet />
        </div>

        <AppRightSideBar isCollapsed={isSidebarCollapsed} toggleSidebar={toggleSidebar} />
      </div>

      {/* Footer and About */}
      <AppAbout />
      <AppFooter />
    </div>
  );
};

export default AppLayout;
