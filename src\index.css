@custom-variant dark (&:where(.dark, .dark *));

@import "tailwindcss";

/* Your additional global styles */
@media (max-width: 768px) {
  .sidebar-collapsed {
    width: 64px;
  }

  .sidebar-expanded {
    width: 256px;
  }

  .sidebar-hidden {
    transform: translateX(-100%);
  }
}

.sidebar:hover {
  width: 256px !important;
}

/* Dark mode for Ant Design Table */
.dark .ant-table {
  background-color: #1f2937 !important; /* Tailwind dark:bg-gray-900 */
  color: white !important; /* Tailwind dark:text-white */
}

/* Table Header */
.dark .ant-table-thead > tr > th {
  background-color: #374151 !important; /* Tailwind dark:bg-gray-800 */
  color: white !important;
}

/* Table Rows */
.dark .ant-table-tbody > tr > td {
  background-color: #1f2937 !important;
  color: white !important;
}

/* Table Borders */
.dark .ant-table-tbody > tr > td,
.dark .ant-table-thead > tr > th {
  border-color: #4b5563 !important; /* Tailwind dark:border-gray-700 */
}


/* Dark mode for Chart.js */
.dark canvas {
  background-color: transparent !important;
  color: white !important;
}

/* Dark mode for Chart.js grid lines */
.dark .chart-container .chartjs-grid {
  stroke: #4b5563 !important; /* Tailwind dark:border-gray-700 */
}

/* Dark mode for Chart.js tooltips */
.dark .chartjs-tooltip {
  background-color: #374151 !important; /* Tailwind dark:bg-gray-800 */
  color: white !important;
  border: 1px solid #4b5563 !important;
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE/Edge */
    scrollbar-width: none;     /* Firefox */
  }
}
