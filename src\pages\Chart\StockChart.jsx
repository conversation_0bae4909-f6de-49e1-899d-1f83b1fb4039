

// import React, { useEffect, useRef, useState } from 'react';
// import { useParams } from 'react-router-dom';
// import axios from 'axios';
// import {
//   createChart,
//   ColorType,
//   CandlestickSeries,
//   LineSeries,
//   BarSeries,
//   AreaSeries,
//   BaselineSeries,
//   HistogramSeries,
// } from 'lightweight-charts';

// const StockChart = () => {
//   const apikey = import.meta.env.VITE_STOCKS_API_KEY;
//   const { symbol } = useParams();
//   const chartContainerRef = useRef(null);
//   const chartRef = useRef(null);
//   const seriesRef = useRef(null);
//   const legendRef = useRef(null);

//   const [status, setStatus] = useState('loading');
//   const [error, setError] = useState(null);
//   const [chartData, setChartData] = useState([]);
//   const [seriesType, setSeriesType] = useState('Candlestick');
//   const [interval, setInterval] = useState('1D');

//   const intervalConfig = {
//     '1D': { apiInterval: '1day', outputsize: 100 },
//     '1W': { apiInterval: '1week', outputsize: 52 },
//     '1M': { apiInterval: '1month', outputsize: 24 },
//     '1Y': { apiInterval: '1month', outputsize: 120 },
//   };

//   // Fetch data based on the selected interval
//   useEffect(() => {
//     const fetchCandles = async () => {
//       try {
//         setStatus('loading');
//         const { apiInterval, outputsize } = intervalConfig[interval];
//         const res = await axios.get(
//           `https://api.twelvedata.com/time_series?symbol=${symbol}&interval=${apiInterval}&outputsize=${outputsize}&apikey=${apikey}`
//         );

//         if (!res.data || !res.data.values) {
//           throw new Error('Invalid response format from Twelve Data API');
//         }

//         const candles = res.data.values.reverse().map(item => ({
//           time: item.datetime.split(' ')[0],
//           open: parseFloat(item.open),
//           high: parseFloat(item.high),
//           low: parseFloat(item.low),
//           close: parseFloat(item.close),
//           value: parseFloat(item.close),
//         }));

//         setChartData(candles);
//         setStatus('success');
//       } catch (err) {
//         console.error('Error fetching data:', err);
//         setStatus('error');
//         setError(err.message);
//       }
//     };

//     if (apikey && symbol) {
//       fetchCandles();
//     } else {
//       setStatus('error');
//       setError('Missing API key or symbol');
//     }
//   }, [symbol, apikey, interval]);

//   // Initialize chart and legend
//   useEffect(() => {
//     if (!chartRef.current && chartContainerRef.current) {
//       const chart = createChart(chartContainerRef.current, {
//         layout: {
//           background: { type: ColorType.Solid, color: '#ffffff' },
//           textColor: '#000',
//         },
//         grid: {
//           vertLines: { visible: false }, // Hide vertical grid lines
//           horzLines: { visible: false }, // Hide horizontal grid lines
//         },
//         rightPriceScale: {
//           scaleMargins: {
//             top: 0.4, // Leave space for the legend
//             bottom: 0.15,
//           },
//         },
//         crosshair: {
//           horzLine: {
//             visible: false, // Hide horizontal crosshair line
//             labelVisible: false,
//           },
//         },
//         width: chartContainerRef.current.clientWidth,
//         height: 500,
//       });

//       chartRef.current = chart;

//       // Create the legend div
//       const legend = document.createElement('div');
//       legend.style = `position: absolute; left: 12px; top: 12px; z-index: 1; font-size: 14px; font-family: sans-serif; line-height: 18px; font-weight: 300;`;
//       legend.style.color = 'black';
//       chartContainerRef.current.appendChild(legend);
//       legendRef.current = legend;

//       const handleResize = () => {
//         if (chartContainerRef.current && chartRef.current) {
//           chartRef.current.applyOptions({ width: chartContainerRef.current.clientWidth });
//         }
//       };

//       window.addEventListener('resize', handleResize);

//       return () => {
//         window.removeEventListener('resize', handleResize);
//         if (chartRef.current) {
//           chartRef.current.remove();
//           chartRef.current = null;
//           seriesRef.current = null;
//           legendRef.current = null;
//         }
//       };
//     }
//   }, []);

//   // Update series and subscribe to crosshair movement for the legend
//   useEffect(() => {
//     if (chartRef.current && chartData.length > 0) {
//       if (seriesRef.current) {
//         chartRef.current.removeSeries(seriesRef.current);
//         seriesRef.current = null;
//       }

//       let newSeries;
//       const commonOptions = {
//         crossHairMarkerVisible: false, // Disable crosshair marker for consistency with example
//       };

//       switch (seriesType) {
//         case 'Candlestick':
//           newSeries = chartRef.current.addSeries(CandlestickSeries, {
//             upColor: '#26a69a',
//             downColor: '#ef5350',
//             borderVisible: false,
//             wickUpColor: '#26a69a',
//             wickDownColor: '#ef5350',
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             open: item.open,
//             high: item.high,
//             low: item.low,
//             close: item.close,
//           })));
//           break;

//         case 'Line':
//           newSeries = chartRef.current.addSeries(LineSeries, {
//             color: '#2962FF',
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             value: item.close,
//           })));
//           break;

//         case 'Bars':
//           newSeries = chartRef.current.addSeries(BarSeries, {
//             upColor: '#26a69a',
//             downColor: '#ef5350',
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             open: item.open,
//             high: item.high,
//             low: item.low,
//             close: item.close,
//           })));
//           break;

//         case 'Area':
//           newSeries = chartRef.current.addSeries(AreaSeries, {
//             lineColor: '#2962FF',
//             topColor: '#2962FF',
//             bottomColor: 'rgba(41, 98, 255, 0.28)',
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             value: item.close,
//           })));
//           break;

//         case 'Baseline':
//           newSeries = chartRef.current.addSeries(BaselineSeries, {
//             topFillColor1: '#26a69a',
//             topFillColor2: 'rgba(38, 166, 154, 0.28)',
//             bottomFillColor1: '#ef5350',
//             bottomFillColor2: 'rgba(239, 83, 80, 0.28)',
//             baseValue: { price: chartData[0]?.close || 0 },
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             value: item.close,
//           })));
//           break;

//         case 'Histogram':
//           newSeries = chartRef.current.addSeries(HistogramSeries, {
//             color: '#26a69a',
//             ...commonOptions,
//           });
//           newSeries.setData(chartData.map(item => ({
//             time: item.time,
//             value: item.close,
//           })));
//           break;

//         default:
//           console.warn('Unknown series type:', seriesType);
//           return;
//       }

//       seriesRef.current = newSeries;

//       // Legend update logic
//       const getLastBar = series => {
//         const lastIndex = series.dataByIndex(Number.MAX_SAFE_INTEGER, -1);
//         return series.dataByIndex(lastIndex);
//       };

//       const formatPrice = price => (Math.round(price * 100) / 100).toFixed(2);

//       const setTooltipHtml = (name, date, price) => {
//         if (legendRef.current) {
//           legendRef.current.innerHTML = `
//             <div style="font-size: 24px; margin: 4px 0px;">${name}</div>
//             <div style="font-size: 22px; margin: 4px 0px;">${price}</div>
//             <div>${date}</div>
//           `;
//         }
//       };

//       const updateLegend = param => {
//         if (!seriesRef.current || !legendRef.current) return;

//         const validCrosshairPoint = !(
//           param === undefined ||
//           param.time === undefined ||
//           param.point?.x < 0 ||
//           param.point?.y < 0
//         );

//         const bar = validCrosshairPoint
//           ? param.seriesData.get(seriesRef.current)
//           : getLastBar(seriesRef.current);

//         if (!bar) return;

//         const time = bar.time;
//         const price = bar.value !== undefined ? bar.value : bar.close;
//         const formattedPrice = formatPrice(price);
//         setTooltipHtml(symbol, time, formattedPrice);
//       };

//       // Subscribe to crosshair movement
//       chartRef.current.subscribeCrosshairMove(updateLegend);

//       // Initial legend update with the last bar
//       updateLegend(undefined);

//       chartRef.current.timeScale().fitContent();

//       return () => {
//         chartRef.current.unsubscribeCrosshairMove(updateLegend);
//       };
//     }
//   }, [chartData, seriesType, symbol]);

//   const chartTypeButtonStyle = {
//     padding: '8px 16px',
//     margin: '0 2px',
//     border: 'none',
//     borderRadius: '4px',
//     backgroundColor: '#333',
//     color: '#fff',
//     cursor: 'pointer',
//     fontSize: '14px',
//   };

//   const chartTypeActiveButtonStyle = {
//     ...chartTypeButtonStyle,
//     backgroundColor: '#555',
//   };

//   const rangeButtonStyle = {
//     padding: '6px 12px',
//     margin: '0 4px',
//     border: '1px solid #ccc',
//     borderRadius: '4px',
//     backgroundColor: '#fff',
//     color: '#000',
//     cursor: 'pointer',
//     fontSize: '14px',
//   };

//   const rangeActiveButtonStyle = {
//     ...rangeButtonStyle,
//     backgroundColor: '#e0e0e0',
//   };

//   return (
//     <div>
//       <h2 className="text-xl font-bold mb-2">Chart: {symbol}</h2>
//       <div >
//         {['Candles', 'Line', 'Bars', 'Area', 'Baseline', 'Histogram'].map(type => (
//           <button
//             key={type}
//             style={seriesType === type ? chartTypeActiveButtonStyle : chartTypeButtonStyle}
//             onClick={() => setSeriesType(type)}
//           >
//             {type}
//           </button>
//         ))}
//       </div>
    
//       {status === 'loading' && <p>Loading data...</p>}
//       {status === 'error' && <p style={{ color: 'red' }}>Error: {error}</p>}
//       <div ref={chartContainerRef} style={{ width: '100%', height: '500px', position: 'relative' }} />
//         <div style={{ marginBottom: '10px' }}>
//         {['1D', '1W', '1M', '1Y'].map(range => (
//           <button
//             key={range}
//             style={interval === range ? rangeActiveButtonStyle : rangeButtonStyle}
//             onClick={() => setInterval(range)}
//           >
//             {range}
//           </button>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default StockChart;

import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import {
  createChart,
  ColorType,
  CandlestickSeries,
  LineSeries,
  BarSeries,
  AreaSeries,
  BaselineSeries,
  HistogramSeries,
} from 'lightweight-charts';

const StockChart = () => {
  const apikey = import.meta.env.VITE_STOCKS_API_KEY;
  const { symbol } = useParams();
  const chartContainerRef = useRef(null);
  const chartRef = useRef(null);
  const seriesRef = useRef(null);
  const legendRef = useRef(null);

  const [status, setStatus] = useState('loading');
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [seriesType, setSeriesType] = useState('Candlestick');
  const [interval, setInterval] = useState('1D');

  const currentLocale = window.navigator.languages[0] || 'en-US';
  const myPriceFormatter = Intl.NumberFormat(currentLocale, {
    style: 'currency',
    currency: 'EUR',
  }).format;

  const intervalConfig = {
    '1D': { apiInterval: '1day', outputsize: 100 },
    '1W': { apiInterval: '1week', outputsize: 52 },
    '1M': { apiInterval: '1month', outputsize: 24 },
    '1Y': { apiInterval: '1month', outputsize: 120 },
  };

  useEffect(() => {
    const fetchCandles = async () => {
      try {
        setStatus('loading');
        const { apiInterval, outputsize } = intervalConfig[interval];
        const res = await axios.get(
          `https://api.twelvedata.com/time_series?symbol=${symbol}&interval=${apiInterval}&outputsize=${outputsize}&apikey=${apikey}`
        );

        if (!res.data || !res.data.values) {
          throw new Error('Invalid response format from Twelve Data API');
        }

        const candles = res.data.values.reverse().map(item => ({
          time: item.datetime.split(' ')[0],
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
          value: parseFloat(item.close),
        }));

        setChartData(candles);
        setStatus('success');
      } catch (err) {
        console.error('Error fetching data:', err);
        setStatus('error');
        setError(err.message);
      }
    };

    if (apikey && symbol) {
      fetchCandles();
    } else {
      setStatus('error');
      setError('Missing API key or symbol');
    }
  }, [symbol, apikey, interval]);

  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#000',
      },
      grid: {
        vertLines: { visible: false },
        horzLines: { visible: false },
      },
      rightPriceScale: {
        scaleMargins: {
          top: 0.4,
          bottom: 0.15,
        },
      },
      crosshair: {
        horzLine: {
          visible: false,
          labelVisible: false,
        },
      },
      localization: {
        priceFormatter: myPriceFormatter,
      },
      width: chartContainerRef.current.clientWidth,
      height: 500,
    });

    chartRef.current = chart;

    const legend = document.createElement('div');
    legend.style = `position: absolute; left: 12px; top: 12px; z-index: 1; font-size: 14px; font-family: sans-serif; line-height: 18px; font-weight: 300;`;
    legend.style.color = 'black';
    chartContainerRef.current.appendChild(legend);
    legendRef.current = legend;

    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({ width: chartContainerRef.current.clientWidth });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
      seriesRef.current = null;
      legendRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (!chartRef.current || chartData.length === 0) return;

    if (seriesRef.current) {
      chartRef.current.removeSeries(seriesRef.current);
      seriesRef.current = null;
    }

    let newSeries;
    const commonOptions = {
      crossHairMarkerVisible: false,
    };

    switch (seriesType) {
      case 'Candlestick':
        newSeries = chartRef.current.addSeries(CandlestickSeries, {
          upColor: '#26a69a',
          downColor: '#ef5350',
          borderVisible: false,
          wickUpColor: '#26a69a',
          wickDownColor: '#ef5350',
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
        })));
        break;

      case 'Line':
        newSeries = chartRef.current.addSeries(LineSeries, {
          color: '#2962FF',
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          value: item.close,
        })));
        break;

      case 'Bars':
        newSeries = chartRef.current.addSeries(BarSeries, {
          upColor: '#26a69a',
          downColor: '#ef5350',
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
        })));
        break;

      case 'Area':
        newSeries = chartRef.current.addSeries(AreaSeries, {
          lineColor: '#2962FF',
          topColor: '#2962FF',
          bottomColor: 'rgba(41, 98, 255, 0.28)',
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          value: item.close,
        })));
        break;

      case 'Baseline':
        newSeries = chartRef.current.addSeries(BaselineSeries, {
          topFillColor1: '#26a69a',
          topFillColor2: 'rgba(38, 166, 154, 0.28)',
          bottomFillColor1: '#ef5350',
          bottomFillColor2: 'rgba(239, 83, 80, 0.28)',
          baseValue: { price: chartData[0]?.close || 0 },
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          value: item.close,
        })));
        break;

      case 'Histogram':
        newSeries = chartRef.current.addSeries(HistogramSeries, {
          color: '#26a69a',
          ...commonOptions,
        });
        newSeries.setData(chartData.map(item => ({
          time: item.time,
          value: item.close,
        })));
        break;

      default:
        console.warn('Unknown series type:', seriesType);
        return;
    }

    seriesRef.current = newSeries;

    const getLastBar = series => {
      const lastIndex = series.dataByIndex(Number.MAX_SAFE_INTEGER, -1);
      return series.dataByIndex(lastIndex);
    };

    const setTooltipHtml = (name, date, price) => {
      if (legendRef.current) {
        const formattedPrice = myPriceFormatter(price);
        legendRef.current.innerHTML = `
          <div style="font-size: 24px; margin: 4px 0px;">${name}</div>
          <div style="font-size: 22px; margin: 4px 0px;">${formattedPrice}</div>
          <div>${date}</div>
        `;
      }
    };

    const updateLegend = param => {
      if (!seriesRef.current || !legendRef.current) return;

      const validCrosshairPoint = !(
        param === undefined ||
        param.time === undefined ||
        param.point?.x < 0 ||
        param.point?.y < 0
      );

      const bar = validCrosshairPoint
        ? param.seriesData.get(seriesRef.current)
        : getLastBar(seriesRef.current);

      if (!bar) return;

      const time = bar.time;
      const price = bar.value !== undefined ? bar.value : bar.close;
      setTooltipHtml(symbol, time, price);
    };

    chartRef.current.subscribeCrosshairMove(updateLegend);
    updateLegend(undefined);
    chartRef.current.timeScale().fitContent();

    return () => {
      // Safely unsubscribe only if chartRef.current exists
      if (chartRef.current) {
        chartRef.current.unsubscribeCrosshairMove(updateLegend);
      }
    };
  }, [chartData, seriesType, symbol]);

  const chartTypeButtonStyle = {
    padding: '8px 16px',
    margin: '0 2px',
    border: 'none',
    borderRadius: '4px',
    backgroundColor: '#333',
    color: '#fff',
    cursor: 'pointer',
    fontSize: '14px',
  };

  const chartTypeActiveButtonStyle = {
    ...chartTypeButtonStyle,
    backgroundColor: '#555',
  };

  const rangeButtonStyle = {
    padding: '6px 12px',
    margin: '0 4px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    backgroundColor: '#fff',
    color: '#000',
    cursor: 'pointer',
    fontSize: '14px',
  };

  const rangeActiveButtonStyle = {
    ...rangeButtonStyle,
    backgroundColor: '#e0e0e0',
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-2">Chart: {symbol}</h2>
      <div style={{ marginBottom: '10px' }}>
        {['Candles', 'Line', 'Bars', 'Area', 'Baseline', 'Histogram'].map(type => (
          <button
            key={type}
            style={seriesType === type ? chartTypeActiveButtonStyle : chartTypeButtonStyle}
            onClick={() => setSeriesType(type)}
          >
            {type}
          </button>
        ))}
      </div>
      <div style={{ marginBottom: '10px' }}>
        {['1D', '1W', '1M', '1Y'].map(range => (
          <button
            key={range}
            style={interval === range ? rangeActiveButtonStyle : rangeButtonStyle}
            onClick={() => setInterval(range)}
          >
            {range}
          </button>
        ))}
      </div>
      {status === 'loading' && <p>Loading data...</p>}
      {status === 'error' && <p style={{ color: 'red' }}>Error: {error}</p>}
      <div ref={chartContainerRef} style={{ width: '100%', height: '500px', position: 'relative' }} />
    </div>
  );
};

export default StockChart;