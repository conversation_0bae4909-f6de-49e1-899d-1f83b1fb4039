import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Table, Dropdown, Menu, Button } from "antd";
import { DownOutlined, EditOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

import MainContentHeader from "../../components/common/MainContentHeader";
import MarketSelector from "../../components/common/MarketSelector";
import StocksFilterModal from "./StocksFilterModal";

import {
  fetchTasi,
  fetchNumo,
  fetchAllMarket,
  fetchIndustriesList,
  fetchByIndustry,
} from "../../redux/stocks/stocksActions";

const Index = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { data, status, industries } = useSelector((state) => state.stocks);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  const [selectedMarket, setSelectedMarket] = useState(
    () => localStorage.getItem("selectedMarket") || "all"
  );
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [dynamicFilters, setDynamicFilters] = useState([]);
  const [selectedIndustry, setSelectedIndustry] = useState(null);

  useEffect(() => {
    // fetch industries once
    dispatch(fetchIndustriesList());
  }, [dispatch]);

  useEffect(() => {
    localStorage.setItem("selectedMarket", selectedMarket);
    setSelectedIndustry(null);
    switch (selectedMarket) {
      case "tasi":
        dispatch(fetchTasi());
        break;
      case "numo":
        dispatch(fetchNumo());
        break;
      default:
        dispatch(fetchAllMarket());
    }
  }, [selectedMarket, dispatch]);

  const onIndustrySelect = ({ key }) => {
    setSelectedIndustry(key);
    dispatch(fetchByIndustry(key));
  };

  const IndustriesMenu = (
  <Menu
    onClick={onIndustrySelect}
    style={{
      maxHeight: "300px", // limit height for scroll
      width: "250px",     // fixed width
      overflowY: "auto",  // enable vertical scrolling
    }}
  >
    {industries.map((ind) => (
      <Menu.Item key={ind}>{ind}</Menu.Item>
    ))}
  </Menu>
);

  const baseColumns = [
    { title: "Symbol", dataIndex: "symbol", key: "symbol" },
    { title: "Company Name", dataIndex: "name", key: "name" },
    { title: "Stock Price", dataIndex: "price", key: "price" },
    { title: "1D Change (%)", dataIndex: "change", key: "change" },
    { title: "Div Yield (%)", dataIndex: "div_yield", key: "div_yield" },
    { title: "P/E Ratio", dataIndex: "pe_ratio", key: "pe_ratio" },
    { title: "52-wk High", dataIndex: "high", key: "high" },
    { title: "52-wk Low", dataIndex: "low", key: "low" },
    { title: "Market Cap", dataIndex: "market_cap", key: "market_cap" },
  ];

  const dynamicColumns = dynamicFilters.map((filter) => ({
    title: filter,
    dataIndex: filter.toLowerCase().replace(/\s+/g, "_"),
    key: filter,
    render: () => "N/A",
  }));

  const combinedColumns = [...baseColumns, ...dynamicColumns];

  const handleFiltersUpdate = useCallback((filters) => {
    setDynamicFilters(filters);
  }, []);

  return (
     <div className="stocks-table bg-white dark:bg-gray-900 dark:text-white p-4">
      <MainContentHeader />

      <div className="w-40 border border-black dark:border-white text-center mx-auto my-4">
        <MarketSelector
          value={selectedMarket}
          onChange={(val) => setSelectedMarket(val)}
        />
      </div>

      <StocksFilterModal
        visible={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        title="Indicators"
        onFiltersApply={handleFiltersUpdate}
        selectedFilters={dynamicFilters}
      />

      <Table
        columns={combinedColumns}
        dataSource={data}
        rowKey="symbol"
        loading={status === "loading"}
        pagination={{
          pageSize,
          current: currentPage,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size);
          },
        }}
        scroll={{ x: "max-content" }}
        onRow={(record) => ({ onClick: () => navigate(`/stocks/${record.symbol}`) })}
        bordered
        title={() => (
          <div
            className="flex justify-between items-center px-2"
            style={{ borderBottom: "1px solid #f0f0f0" }}
          >
            {/* Industry selector in left corner */}
            <Dropdown
    overlay={IndustriesMenu}
    placement="bottomLeft"
    getPopupContainer={(trigger) => trigger.parentElement} // ensure it positions relative to parent
  >
    <Button>
      {selectedIndustry || "Select Industry"} <DownOutlined />
    </Button>
  </Dropdown>

            {/* Edit filters button on right */}
            <Button
              icon={<EditOutlined />}
              className="border px-3 py-1"
              onClick={() => setIsFilterModalOpen(true)}
            >
              Edit
            </Button>
          </div>
        )}
      />
    </div>
  );
};

export default Index;
