// src/components/Screener/DataPanel.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import FilterModal from './FilterModal';
import {
  loadScreenerOverview,
  loadStocksByIndustry,
  loadFilteredStocks
} from '../../redux/screener/screenerSlice';
import { Button, Space, Select, AutoComplete } from 'antd';
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const marketCapOptions = ['1B', '5B', '10B', '50B', '100B'].map(v => ({ label: v, value: v }));
const priceOptions = ['1', '50', '100'].map(v => ({ label: v, value: v }));

export default function DataPanel() {
  const dispatch = useDispatch();

  // Quick-add states
  const [modalVisible, setModalVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [options, setOptions] = useState([]);

  // Inline filter states
  const [marketCapCond, setMarketCapCond] = useState('Any');
  const [marketCapValue, setMarketCapValue] = useState(null);
  const [stockPriceCond, setStockPriceCond] = useState('Any');
  const [stockPriceValue, setStockPriceValue] = useState(null);

  // Industry states
  const [industry, setIndustry] = useState('Any');
  const [industries, setIndustries] = useState([]);

  // Dropdown state and ref
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Fetch industries once
  useEffect(() => {
    axios.get(`${import.meta.env.VITE_BACKEND_URL}/industries/get-all-industries`)
      .then(r => setIndustries(r.data.response.data.industries || []))
      .catch(console.error);
  }, []);

  // Dispatch on market cap/price filter change
  useEffect(() => {
    const f = {};
    if (marketCapCond !== 'Any' && marketCapValue) {
      f.marketCap = (marketCapCond === 'Less Than' ? '<' : '>') + marketCapValue;
    }
    if (stockPriceCond !== 'Any' && stockPriceValue) {
      f.stockPrice = (stockPriceCond === 'Less Than' ? '<' : '>') + stockPriceValue;
    }
    if (Object.keys(f).length) {
      dispatch(loadFilteredStocks(f));
    } else if (industry === 'Any') {
      // No inline filters and no industry → reset overview
      dispatch(loadScreenerOverview());
    }
  }, [marketCapCond, marketCapValue, stockPriceCond, stockPriceValue, industry, dispatch]);

  // Industry dropdown handler
  const handleIndustryChange = (val) => {
    setIndustry(val);
    if (val === 'Any') {
      // Reset to full overview when "Any" picked
      dispatch(loadScreenerOverview());
    } else {
      dispatch(loadStocksByIndustry(val));
    }
  };

  // Quick-add stubs
  const handleSearch = (v) => { /* Implement search logic here if needed */ };
  const handleSelect = (v) => { /* Implement select logic here if needed */ };

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  return (
    <div className="p-4 flex flex-col gap-4">
      {/* Filters Button */}
      <Button onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
        Filters <DownOutlined />
      </Button>

      {/* Dropdown Content */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          style={{
            width: '100%',
            padding: 12,
            background: '#fff',
            borderRadius: 4,
            border: '1px solid #d9d9d9',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
          }}
        >
          <Space style={{ width: '100%', marginBottom: 12 }} align="start">
            <Button
              type="primary" // Blue background
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
            >
              Add Filter
            </Button>
            <AutoComplete
              options={options}
              onSelect={handleSelect}
              onSearch={handleSearch}
              value={searchTerm}
              placeholder="Search filters…"
              allowClear
              style={{ flex: 1 }}
            />
          </Space>
        </div>
      )}

      {/* Inline Filters */}
      <div className="flex flex-wrap gap-8">
        {/* Market Cap */}
        <Space align="center">
          <span className="font-medium">Market Cap:</span>
          <Select
            value={marketCapCond}
            onChange={(v) => { setMarketCapCond(v); setMarketCapValue(null); }}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
          {marketCapCond !== 'Any' && (
            <Select
              value={marketCapValue}
              onChange={setMarketCapValue}
              placeholder="Value"
              style={{ width: 120 }}
            >
              {marketCapOptions.map(o => (
                <Option key={o.value} value={o.value}>{o.label}</Option>
              ))}
            </Select>
          )}
        </Space>

        {/* Industry */}
        <Space align="center">
          <span className="font-medium">Industry:</span>
          <Select
            value={industry}
            onChange={handleIndustryChange}
            style={{ width: 200 }}
          >
            <Option value="Any">Any</Option>
            {industries.map(i => (
              <Option key={i} value={i}>{i}</Option>
            ))}
          </Select>
        </Space>

        {/* Stock Price */}
        <Space align="center">
          <span className="font-medium">Stock Price:</span>
          <Select
            value={stockPriceCond}
            onChange={(v) => { setStockPriceCond(v); setStockPriceValue(null); }}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
          {stockPriceCond !== 'Any' && (
            <Select
              value={stockPriceValue}
              onChange={setStockPriceValue}
              placeholder="Value"
              style={{ width: 100 }}
            >
              {priceOptions.map(o => (
                <Option key={o.value} value={o.value}>{o.label}</Option>
              ))}
            </Select>
          )}
        </Space>
      </div>

      {/* Filter Modal */}
      <FilterModal visible={modalVisible} onClose={() => setModalVisible(false)} />
    </div>
  );
}