import React from "react";
import { useTranslation } from "react-i18next";

const MarketSelector = ({ value, onChange }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  const btnBase =
    "p-2 w-full dark:bg-gray-700 dark:text-white focus:outline-none";
  const leftBtnBorder = "border-r border-black dark:border-gray-600";

  const activeClass = "bg-gray-200 dark:bg-gray-600 font-semibold";

  return (
    <div
      className={`w-40 border border-black dark:border-gray-600 text-center dark:text-white ${
        isRTL ? "rtl" : "ltr"
      }`}
    >
      <div className="grid grid-cols-2 border-b border-black dark:border-gray-600">
        <button
          onClick={() => onChange("tasi")}
          className={`${btnBase} ${leftBtnBorder} ${
            value === "tasi" ? activeClass : ""
          }`}
        >
          {t("market.tasi").replace("> ", "")}
        </button>
        <button
          onClick={() => onChange("numo")}
          className={`${btnBase} ${
            value === "numo" ? activeClass : ""
          }`}
        >
          {t("market.numo").replace("> ", "")}
        </button>
      </div>
      <button
        onClick={() => onChange("all")}
        className={`${btnBase} ${
          value === "all" ? activeClass : ""
        }`}
      >
        {t("market.allMarkets")}
      </button>
    </div>
  );
};

export default MarketSelector;
