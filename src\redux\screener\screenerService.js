import axios from 'axios';
const backendURL = import.meta.env.VITE_BACKEND_URL;

export const fetchStockScreenerOverview = async () => {
  const res = await axios.get(`${backendURL}/screener/stocks-screener-overview`);
  return res.data.response.data;
};

export const fetchStocksByIndustry = async (industry) => {
  const res = await axios.get(
    `${backendURL}/industries/search-by-industries`,
    { params: { industry } }
  );
  return res.data.response.data;
};

export const fetchFilteredStocks = async ({ marketCap, stockPrice }) => {
  const res = await axios.get(
    `${backendURL}/screener/stocks-screener-filter`,
    { params: { marketCap, stockPrice } }
  );
  return res.data;
};

// ── New endpoints ────────────────────────────────────────────────────────────
export const fetchPerformance = async () => {
  const res = await axios.get(`${backendURL}/screener/stocks-screener-performance`);
  return res.data.response.data;
};

export const fetchEarnings = async () => {
  const res = await axios.get(`${backendURL}/screener/stocks-screener-earnings`);
  return res.data.response.data;
};
