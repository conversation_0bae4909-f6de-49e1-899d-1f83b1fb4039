import PropTypes from "prop-types";

const Header = ({ header }) => {
  if (!header) {
    return (
      <div className="flex justify-center items-center bg-gray-100 dark:bg-gray-900 text-black dark:text-white p-4">
        <p>Loading stock details...</p>
      </div>
    );
  }

  const { name, symbol, price, change, lastUpdatedAt, logo } = header;

  const isNegative = change.trim().startsWith("-");

  return (
    <div className="header-container flex flex-col md:flex-row justify-between items-start md:items-center bg-gray-100 dark:bg-gray-900 text-black dark:text-white p-4 gap-4">
      <div className="flex items-center gap-2">
        {logo && (
          <img
            src={logo}
            alt={`${symbol} logo`}
            className="w-10 h-10 rounded-full object-cover"
          />
        )}
        <h1 className="text-2xl font-bold">
          {name} <span className="text-xl font-medium">({symbol})</span>
        </h1>
      </div>

      <div className="text-lg md:text-xl">
        {price}{" "}
        <span className={`ml-2 font-semibold ${isNegative ? "text-red-600" : "text-green-600"}`}>
          {change}
        </span>
      </div>

      <div className="text-sm md:text-base">
        Last updated:{" "}
        <time dateTime={new Date(lastUpdatedAt).toISOString()}>
          {new Date(lastUpdatedAt).toLocaleString()}
        </time>
      </div>
    </div>
  );
};

Header.propTypes = {
  header: PropTypes.shape({
    name: PropTypes.string.isRequired,
    symbol: PropTypes.string.isRequired,
    price: PropTypes.string.isRequired,
    change: PropTypes.string.isRequired,
    lastUpdatedAt: PropTypes.string.isRequired,
    logo: PropTypes.string,
  }),
};

Header.defaultProps = {
  header: null,
};

export default Header;
