
// import { useEffect, useRef, useState } from 'react';
// function useTwelveDataWebSocket(symbol ) {
//   const apiKey = '5688324e509d4f84b4e03cad2f60bf27'
//   const [candles, setCandles] = useState([]);
//   const [latestPrice, setLatestPrice] = useState(null);
//   const [status, setStatus] = useState('connecting');
//   const [subscriptionStatus, setSubscriptionStatus] = useState(null);
//   const [error, setError] = useState(null);
  
//   const ws = useRef(null);
//   const heartbeatInterval = useRef(null);

//   // Current working candle (mutable)
//   const currentCandleRef = useRef(null);
//   const currentMinuteRef = useRef(null);

//   useEffect(() => {
//     if (!symbol || !apiKey) {
//       setError('Symbol or API key missing');
//       setStatus('error');
//       return;
//     }

//     const wsUrl = `wss://ws.twelvedata.com/v1/quotes/price?apikey=${apiKey}`;
//     ws.current = new WebSocket(wsUrl);

//     ws.current.onopen = () => {
//       setStatus('connected');
//       const subscribeMsg = {
//         action: 'subscribe',
//         params: { symbols: symbol.toUpperCase() },
//       };
//       ws.current.send(JSON.stringify(subscribeMsg));

//       heartbeatInterval.current = setInterval(() => {
//         ws.current?.send(JSON.stringify({ action: 'heartbeat' }));
//       }, 10000);
//     };

//     ws.current.onmessage = (event) => {
//       try {
//         const message = JSON.parse(event.data);

//         if (message.event === 'price') {
//           const timestamp = message.timestamp; // in seconds
//           const price = message.price;
//           const minuteTime = Math.floor(timestamp / 60) * 60;

//           setLatestPrice(price);

//           setCandles(prevCandles => {
//             const currentMinute = currentMinuteRef.current;
//             const currentCandle = currentCandleRef.current;

//             if (currentMinute === null || minuteTime !== currentMinute) {
//               // Push the last candle to history
//               const updated = currentCandle ? [...prevCandles.slice(-99), currentCandle] : [...prevCandles];

//               // Start a new candle
//               const newCandle = {
//                 time: minuteTime,
//                 open: price,
//                 high: price,
//                 low: price,
//                 close: price,
//               };

//               currentMinuteRef.current = minuteTime;
//               currentCandleRef.current = newCandle;

//               return updated;
//             } else {
//               // Update existing candle
//               const updatedCandle = {
//                 ...currentCandle,
//                 high: Math.max(currentCandle.high, price),
//                 low: Math.min(currentCandle.low, price),
//                 close: price,
//               };

//               currentCandleRef.current = updatedCandle;

//               return prevCandles;
//             }
//           });
//         } else if (message.event === 'subscribe-status') {
//           setSubscriptionStatus(message.status);
//           if (message.status !== 'ok') {
//             setError(`Subscription failed: ${message.fails?.join(', ') || 'unknown error'}`);
//           }
//         }
//       } catch (err) {
//         setError('Failed to parse WebSocket message');
//       }
//     };

//     ws.current.onerror = () => {
//       setStatus('error');
//       setError('WebSocket connection error');
//     };

//     ws.current.onclose = () => {
//       setStatus('closed');
//       clearInterval(heartbeatInterval.current);
//     };

//     return () => {
//       clearInterval(heartbeatInterval.current);
//       if (ws.current && ws.current.readyState === WebSocket.OPEN) {
//         ws.current.send(JSON.stringify({
//           action: 'unsubscribe',
//           params: { symbols: symbol.toUpperCase() },
//         }));
//         ws.current.close();
//       }
//     };
//   }, [symbol, apiKey]);

//   return {
//     candles: [...candles, currentCandleRef.current].filter(Boolean),
//     latestPrice,
//     status,
//     subscriptionStatus,
//     error,
//   };
// }

// export default useTwelveDataWebSocket;
// import { useEffect, useRef, useState } from 'react';

// function useTwelveDataWebSocket(symbol) {
//   const apiKey = '5688324e509d4f84b4e03cad2f60bf27'; // Consider moving to environment variable
//   const [candles, setCandles] = useState([]);
//   const [latestPrice, setLatestPrice] = useState(null);
//   const [status, setStatus] = useState('connecting');
//   const [error, setError] = useState(null);

//   const ws = useRef(null);
//   const heartbeatInterval = useRef(null);
//   const reconnectTimeout = useRef(null);
//   const currentCandleRef = useRef(null);
//   const currentMinuteRef = useRef(null);
//   const reconnectAttempts = useRef(0);
//   const maxReconnectAttempts = 5;

//   const connectWebSocket = () => {
//     if (!symbol || !apiKey) {
//       setError('Symbol or API key missing');
//       setStatus('error');
//       return;
//     }

//     const wsUrl = `wss://ws.twelvedata.com/v1/quotes/price?apikey=${apiKey}`;
//     ws.current = new WebSocket(wsUrl);

//     ws.current.onopen = () => {
//       setStatus('connected');
//       setError(null);
//       reconnectAttempts.current = 0;
//       ws.current.send(
//         JSON.stringify({
//           action: 'subscribe',
//           params: { symbols: symbol.toUpperCase() },
//         })
//       );

//       heartbeatInterval.current = setInterval(() => {
//         if (ws.current?.readyState === WebSocket.OPEN) {
//           ws.current.send(JSON.stringify({ action: 'heartbeat' }));
//         }
//       }, 10000);
//     };

//     ws.current.onmessage = (event) => {
//       try {
//         const message = JSON.parse(event.data);

//         if (message.event === 'price') {
//           const timestamp = message.timestamp; // in seconds
//           const price = parseFloat(message.price);
//           const minuteTime = Math.floor(timestamp / 60) * 60;

//           setLatestPrice(price);

//           if (currentMinuteRef.current === null || minuteTime !== currentMinuteRef.current) {
//             if (currentCandleRef.current) {
//               setCandles((prev) => [...prev.slice(-99), currentCandleRef.current]);
//             }

//             currentCandleRef.current = {
//               time: minuteTime, // Unix timestamp in seconds for lightweight-charts
//               open: price,
//               high: price,
//               low: price,
//               close: price,
//             };
//             currentMinuteRef.current = minuteTime;
//           } else {
//             currentCandleRef.current = {
//               ...currentCandleRef.current,
//               high: Math.max(currentCandleRef.current.high, price),
//               low: Math.min(currentCandleRef.current.low, price),
//               close: price,
//             };
//           }
//         } else if (message.event === 'subscribe-status' && message.status !== 'ok') {
//           setError(`Subscription failed: ${message.fails?.join(', ') || 'unknown error'}`);
//           setStatus('error');
//         }
//       } catch (err) {
//         setError('Failed to parse WebSocket message');
//         setStatus('error');
//       }
//     };

//     ws.current.onerror = () => {
//       setStatus('error');
//       setError('WebSocket connection error');
//       attemptReconnect();
//     };

//     ws.current.onclose = () => {
//       setStatus('closed');
//       clearInterval(heartbeatInterval.current);
//       attemptReconnect();
//     };
//   };

//   const attemptReconnect = () => {
//     if (reconnectAttempts.current < maxReconnectAttempts) {
//       reconnectAttempts.current += 1;
//       const delay = Math.min(1000 * 2 ** reconnectAttempts.current, 30000);
//       reconnectTimeout.current = setTimeout(() => {
//         setStatus('connecting');
//         connectWebSocket();
//       }, delay);
//     } else {
//       setError('Max reconnection attempts reached');
//       setStatus('error');
//     }
//   };

//   useEffect(() => {
//     connectWebSocket();

//     return () => {
//       clearInterval(heartbeatInterval.current);
//       clearTimeout(reconnectTimeout.current);
//       if (ws.current) {
//         if (ws.current.readyState === WebSocket.OPEN) {
//           ws.current.send(
//             JSON.stringify({
//               action: 'unsubscribe',
//               params: { symbols: symbol.toUpperCase() },
//             })
//           );
//           ws.current.close();
//         }
//         ws.current = null;
//       }
//     };
//   }, [symbol, apiKey]);

//   return {
//     candles: [...candles, currentCandleRef.current].filter(Boolean),
//     latestPrice,
//     status,
//     error,
//   };
// }

// export default useTwelveDataWebSocket;


// import { useEffect, useRef, useState } from 'react';

// function useTwelveDataWebSocket(symbol) {
//   const apiKey = '5688324e509d4f84b4e03cad2f60bf27'; // Move to env variable in production
//   const [candles, setCandles] = useState([]);
//   const [latestPrice, setLatestPrice] = useState(null);
//   const [status, setStatus] = useState('connecting');
//   const [error, setError] = useState(null);

//   const ws = useRef(null);
//   const heartbeatInterval = useRef(null);
//   const reconnectTimeout = useRef(null);
//   const currentCandleRef = useRef(null);
//   const currentMinuteRef = useRef(null);
//   const reconnectAttempts = useRef(0);
//   const maxReconnectAttempts = 5;

//   const connectWebSocket = () => {
//     if (!symbol || !apiKey) {
//       setError('Symbol or API key missing');
//       setStatus('error');
//       return;
//     }

//     const wsUrl = `wss://ws.twelvedata.com/v1/quotes/price?apikey=${apiKey}`;
//     ws.current = new WebSocket(wsUrl);

//     ws.current.onopen = () => {
//       setStatus('connected');
//       setError(null);
//       reconnectAttempts.current = 0;
//       ws.current.send(
//         JSON.stringify({
//           action: 'subscribe',
//           params: { symbols: symbol.toUpperCase() },
//         })
//       );

//       heartbeatInterval.current = setInterval(() => {
//         if (ws.current?.readyState === WebSocket.OPEN) {
//           ws.current.send(JSON.stringify({ action: 'heartbeat' }));
//         }
//       }, 10000);
//     };

//     ws.current.onmessage = (event) => {
//       try {
//         const message = JSON.parse(event.data);

//         if (message.event === 'price') {
//           const timestamp = message.timestamp; // in seconds
//           const price = parseFloat(message.price);
//           const minuteTime = Math.floor(timestamp / 60) * 60;

//           setLatestPrice(price);

//           if (currentMinuteRef.current === null || minuteTime !== currentMinuteRef.current) {
//             if (currentCandleRef.current) {
//               setCandles((prev) => [...prev.slice(-99), currentCandleRef.current]);
//             }

//             currentCandleRef.current = {
//               time: minuteTime, // Unix timestamp in seconds
//               open: price,
//               high: price,
//               low: price,
//               close: price,
//             };
//             currentMinuteRef.current = minuteTime;
//           } else {
//             currentCandleRef.current = {
//               ...currentCandleRef.current,
//               high: Math.max(currentCandleRef.current.high, price),
//               low: Math.min(currentCandleRef.current.low, price),
//               close: price,
//             };
//           }
//         } else if (message.event === 'subscribe-status' && message.status !== 'ok') {
//           setError(`Subscription failed: ${message.fails?.join(', ') || 'unknown error'}`);
//           setStatus('error');
//         }
//       } catch (err) {
//         setError('Failed to parse WebSocket message');
//         setStatus('error');
//       }
//     };

//     ws.current.onerror = () => {
//       setStatus('error');
//       setError('WebSocket connection error');
//       attemptReconnect();
//     };

//     ws.current.onclose = () => {
//       setStatus('closed');
//       clearInterval(heartbeatInterval.current);
//       attemptReconnect();
//     };
//   };

//   const attemptReconnect = () => {
//     if (reconnectAttempts.current < maxReconnectAttempts) {
//       reconnectAttempts.current += 1;
//       const delay = Math.min(1000 * 2 ** reconnectAttempts.current, 30000);
//       reconnectTimeout.current = setTimeout(() => {
//         setStatus('connecting');
//         connectWebSocket();
//       }, delay);
//     } else {
//       setError('Max reconnection attempts reached');
//       setStatus('error');
//     }
//   };

//   useEffect(() => {
//     connectWebSocket();

//     return () => {
//       clearInterval(heartbeatInterval.current);
//       clearTimeout(reconnectTimeout.current);
//       if (ws.current) {
//         if (ws.current.readyState === WebSocket.OPEN) {
//           ws.current.send(
//             JSON.stringify({
//               action: 'unsubscribe',
//               params: { symbols: symbol.toUpperCase() },
//             })
//           );
//           ws.current.close();
//         }
//         ws.current = null;
//       }
//     };
//   }, [symbol, apiKey]);

//   return {
//     candles: [...candles, currentCandleRef.current].filter(Boolean),
//     latestPrice,
//     status,
//     error,
//   };
// }

// export default useTwelveDataWebSocket;

import { useEffect, useRef, useState } from 'react';

function useTwelveDataWebSocket(symbol) {
  const apiKey = import.meta.env.VITE_STOCKS_API_KEY 
  const [candles, setCandles] = useState([]);
  const [latestPrice, setLatestPrice] = useState(null);
  const [status, setStatus] = useState('connecting');
  const [error, setError] = useState(null);

  const ws = useRef(null);
  const heartbeatInterval = useRef(null);
  const reconnectTimeout = useRef(null);
  const currentCandleRef = useRef(null);
  const currentMinuteRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connectWebSocket = () => {
    if (!symbol || !apiKey) {
      setError('Symbol or API key missing');
      setStatus('error');
      return;
    }

    const wsUrl = `wss://ws.twelvedata.com/v1/quotes/price?apikey=${apiKey}`;
    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      // Ensure WebSocket is OPEN before sending
      if (ws.current.readyState === WebSocket.OPEN) {
        setStatus('connected');
        setError(null);
        reconnectAttempts.current = 0;
        ws.current.send(
          JSON.stringify({
            action: 'subscribe',
            params: { symbols: symbol.toUpperCase() },
          })
        );

        heartbeatInterval.current = setInterval(() => {
          if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({ action: 'heartbeat' }));
          }
        }, 10000);
      } else {
        setTimeout(() => {
          if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(
              JSON.stringify({
                action: 'subscribe',
                params: { symbols: symbol.toUpperCase() },
              })
            );
          }
        }, 100);
      }
    };

    ws.current.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        if (message.event === 'price') {
          const timestamp = message.timestamp; // in seconds
          const price = parseFloat(message.price);
          const minuteTime = Math.floor(timestamp / 60) * 60;

          setLatestPrice(price);

          if (currentMinuteRef.current === null || minuteTime !== currentMinuteRef.current) {
            if (currentCandleRef.current) {
              // Only add completed candles to state
              setCandles((prev) => {
                const newCandles = [...prev, currentCandleRef.current];
                // Sort and remove duplicates by time
                return newCandles
                  .filter(
                    (candle, index, self) =>
                      index === self.findIndex((c) => c.time === candle.time)
                  )
                  .sort((a, b) => a.time - b.time)
                  .slice(-100); // Limit to 100 candles
              });
            }

            currentCandleRef.current = {
              time: minuteTime, // Unix timestamp in seconds
              open: price,
              high: price,
              low: price,
              close: price,
            };
            currentMinuteRef.current = minuteTime;
          } else {
            currentCandleRef.current = {
              ...currentCandleRef.current,
              high: Math.max(currentCandleRef.current.high, price),
              low: Math.min(currentCandleRef.current.low, price),
              close: price,
            };
          }
        } else if (message.event === 'subscribe-status' && message.status !== 'ok') {
          setError(`Subscription failed: ${message.fails?.join(', ') || 'unknown error'}`);
          setStatus('error');
        }
      } catch (err) {
        setError('Failed to parse WebSocket message');
        setStatus('error');
      }
    };

    ws.current.onerror = () => {
      setStatus('error');
      setError('WebSocket connection error');
      attemptReconnect();
    };

    ws.current.onclose = () => {
      setStatus('closed');
      clearInterval(heartbeatInterval.current);
      attemptReconnect();
    };
  };

  const attemptReconnect = () => {
    if (reconnectAttempts.current < maxReconnectAttempts) {
      reconnectAttempts.current += 1;
      const delay = Math.min(1000 * 2 ** reconnectAttempts.current, 30000);
      reconnectTimeout.current = setTimeout(() => {
        setStatus('connecting');
        connectWebSocket();
      }, delay);
    } else {
      setError('Max reconnection attempts reached');
      setStatus('error');
    }
  };

  useEffect(() => {
    connectWebSocket();

    return () => {
      clearInterval(heartbeatInterval.current);
      clearTimeout(reconnectTimeout.current);
      if (ws.current) {
        if (ws.current.readyState === WebSocket.OPEN) {
          ws.current.send(
            JSON.stringify({
              action: 'unsubscribe',
              params: { symbols: symbol.toUpperCase() },
            })
          );
          ws.current.close();
        }
        ws.current = null;
      }
    };
  }, [symbol, apiKey]);

  // Only return completed candles, exclude currentCandleRef
  return {
    candles,
    latestPrice,
    status,
    error,
  };
}

export default useTwelveDataWebSocket;
