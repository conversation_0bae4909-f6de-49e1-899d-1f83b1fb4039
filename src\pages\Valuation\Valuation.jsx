import MainContentHeader from "../../components/common/MainContentHeader";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import ValuationTabs from "./ValuationTabs";
import CompanySearch from "./CompanySearch";
import ValuationTable from "./ValuationTable";
import StockInfoCard from "./StockInfoCard";
import ValuationResults from "./ValuationResults";

const Valuation = () => {
  const { t } = useTranslation();
  // const { t, i18n } = useTranslation();
  // const isRTL = i18n.dir() === "rtl";

  // State for active valuation method
  const [activeTab, setActiveTab] = useState("pe");

  // Mock stock data
  const [stockData] = useState({
    name: "Saudi Aramco Base",
    ticker: "2222",
    price: 27.85,
    currency: "Sar",
    change: -0.15,
    changePercent: -0.54,
    timestamp: "Dec 29, 2024, 3:19 PM AST",
  });

  // Mock historical performance data
  const [historicalData] = useState({
    netProfitGrowth: {
      oneYear: "5%",
      threeYears: "3%",
      fiveYears: "2%",
    },
    pe: {
      oneYear: "13x",
      threeYears: "11x",
      fiveYears: "10x",
    },
  });

  // User estimation state
  const [estimations, setEstimations] = useState({
    netProfitGrowth: {
      low: "3%",
      mid: "4%",
      high: "5%",
    },
    pe: {
      low: "10x",
      mid: "11x",
      high: "13x",
    },
    marginOfSafety: "8%",
    yearsOfAnalysis: "5",
  });

  // Results state
  const [results] = useState({
    low: {
      price: "27.85",
      upside: "0%",
    },
    mid: {
      price: "30",
      upside: "7%",
    },
    high: {
      price: "35",
      upside: "25.6%",
    },
  });

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Handle analyze action
  const handleAnalyze = () => {
    console.log("Analyzing with current data");
    // Calculation logic would go here
  };

  // Handle save valuation
  const handleSaveValuation = () => {
    console.log("Saving valuation");
    // Save logic would go here
  };

  // Update estimation value
  const handleEstimationChange = (field, subfield, value) => {
    setEstimations((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        [subfield]: value,
      },
    }));
  };

  // Update specific estimation field
  const handleSpecificFieldChange = (field, value) => {
    setEstimations((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="bg-white dark:bg-gray-900 min-h-screen p-4">
      <div className="container mx-auto">
        {/* Header with title */}
        <div className="mb-6">
          <MainContentHeader />
        </div>

        {/* Search bar */}
        <div className="mb-6">
          <CompanySearch />
        </div>

        {/* Tab and Table container - grouped together */}
        <div className="mb-6">
          {/* Tabs */}
          <ValuationTabs activeTab={activeTab} onTabChange={handleTabChange} />

          {/* Content based on active tab */}
          <ValuationTable
            activeTab={activeTab}
            historicalData={historicalData}
            estimations={estimations}
            onEstimationChange={handleEstimationChange}
            onSpecificFieldChange={handleSpecificFieldChange}
          />
        </div>

        {/* Stock info and analyze button */}
        <div className="mb-6 flex items-center gap-4">
          <div className="flex-grow">
            <StockInfoCard stockData={stockData} />
          </div>
          <button
            onClick={handleAnalyze}
            className="bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-12 rounded-lg font-medium text-lg h-full min-w-[180px]"
          >
            {t("valuation.analyze") || "Analyze"}
          </button>
        </div>

        {/* Results */}
        <div className="flex justify-center mb-6">
          <ValuationResults
            results={results}
            currency={stockData.currency}
            onSave={handleSaveValuation}
          />
        </div>
      </div>
    </div>
  );
};

export default Valuation;
