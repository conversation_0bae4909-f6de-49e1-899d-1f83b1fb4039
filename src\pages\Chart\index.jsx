import { useState, useEffect, useMemo, useCallback } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Fuse from 'fuse.js';
import MainContentHeader from "../../components/common/MainContentHeader";

const Index = () => {
   const allStocks = useSelector((state) => state.stocks.data, shallowEqual);
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isFocused, setIsFocused] = useState(false);
  const navigate = useNavigate();

  // Memoized Fuse.js instance for efficient searching
  const fuse = useMemo(() => {
    if (!allStocks || allStocks.length === 0) return null;
    
    return new Fuse(allStocks, {
      keys: [
        { name: 'symbol', weight: 0.7 },
        { name: 'name', weight: 0.3 }
      ],
      threshold: 0.3,
      includeScore: true,
      minMatchCharLength: 2
    });
  }, [allStocks]);

  // Debounced search effect
  useEffect(() => {
    if (!fuse || searchQuery.trim() === '') {
      setSuggestions([]);
      return;
    }

    const timer = setTimeout(() => {
      try {
        const results = fuse.search(searchQuery);
        setSuggestions(results.slice(0, 5).map(result => result.item));
      } catch (error) {
        console.error('Search error:', error);
        setSuggestions([]);
      }
    }, 200);

    return () => clearTimeout(timer);
  }, [searchQuery, fuse]);

  // Handle suggestion selection
  const handleSuggestionClick = useCallback((symbol) => {
    if (!symbol) return;
    
    console.log('Navigating to:', `/chart/${symbol}`); // For debugging
    navigate(`/chart/${symbol}`);
    setSearchQuery('');
    setSuggestions([]);
  }, [navigate]);

  // Handle example stock clicks
  const handleExampleClick = (symbol) => {
    setSearchQuery(symbol);
    handleSuggestionClick(symbol);
  };

  // Prevent blur from hiding suggestions before click registers
  const handleSuggestionMouseDown = (e) => {
    e.preventDefault();
  };

  return (
    <>
      <MainContentHeader />
      <div className="max-w-xl mx-auto mt-12 text-center p-4">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
          Technical Analysis Stock Charts
        </h1>
        <p className="mt-2 text-gray-600 text-sm md:text-base">
          Search for a stock symbol to view an interactive chart with
          technical indicators, drawing tools, and comparison features.
        </p>

        <div className="mt-6 relative">
          <div className="relative w-full max-w-md mx-auto">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setTimeout(() => setIsFocused(false), 300)}
              placeholder="Search stocks by symbol or name..."
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Stock search input"
              aria-haspopup="listbox"
              aria-expanded={suggestions.length > 0}
            />
            
            {isFocused && suggestions.length > 0 && (
              <ul 
                className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
                role="listbox"
              >
                {suggestions.map((stock) => (
                  <li
                    key={stock.symbol}
                    onMouseDown={handleSuggestionMouseDown}
                    onClick={() => handleSuggestionClick(stock.symbol)}
                    className="p-3 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                    role="option"
                  >
                    <span className="font-semibold text-gray-800">{stock.symbol}</span>
                    <span className="text-gray-600 text-sm truncate ml-2">{stock.name}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

        <div className="mt-4 text-sm text-gray-500">
          Examples:{" "}
          <button 
            onClick={() => handleExampleClick('NVDA')} 
            className="text-blue-600 hover:underline mx-1"
          >
            NVDA
          </button>
          <button 
            onClick={() => handleExampleClick('AAPL')} 
            className="text-blue-600 hover:underline mx-1"
          >
            AAPL
          </button>
          <button 
            onClick={() => handleExampleClick('SPY')} 
            className="text-blue-600 hover:underline mx-1"
          >
            SPY
          </button>
          <button 
            onClick={() => handleExampleClick('QQQ')} 
            className="text-blue-600 hover:underline mx-1"
          >
            QQQ
          </button>
        </div>
      </div>
    </>
  );
};
export default Index