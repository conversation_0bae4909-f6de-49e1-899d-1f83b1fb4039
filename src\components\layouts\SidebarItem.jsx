import { Link, useLocation } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const SidebarItem = ({ href, label, isCollapsed, icon }) => {
  const location = useLocation();
  const isActive = location.pathname === href;
  const { i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  // Determine text margin class based on language direction
  const textMarginClass = isRTL ? "mr-4" : "ml-4";

  return (
    <li className="group">
      <Link
        to={href}
        className={`flex items-center py-2.5 px-4 transition hover:bg-white dark:hover:bg-gray-700 ${
          isActive
            ? "bg-white dark:bg-gray-800 border-y-2 border-black dark:border-white"
            : ""
        }`}
      >
        <FontAwesomeIcon icon={icon} className="text-black dark:text-white" />
        <span
          className={`text-black dark:text-white text-sm font-medium leading-tight ${textMarginClass} text-nowrap ${
            isCollapsed ? "hidden" : "block"
          }`}
        >
          {label || "Menu Item"}
        </span>
      </Link>
    </li>
  );
};

SidebarItem.propTypes = {
  href: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  isCollapsed: PropTypes.bool.isRequired,
  icon: PropTypes.object.isRequired,
};

export default SidebarItem;
