// // src/components/SearchBox.jsx
// import { useState, useMemo, useEffect } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { setSearchTerm, clearSearch } from "../../redux/search/searchSlice";
// import { useNavigate } from "react-router-dom";
// import debounce from "lodash.debounce";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faSearch } from "@fortawesome/free-solid-svg-icons";

// export default function SearchBox({ instanceId }) {
//   console.log(instanceId)
//   const dispatch = useDispatch();
//   const navigate = useNavigate();

//   // from redux
//   const { term, show, activeInstance } = useSelector((s) => ({
//     term: s.search.term,
//     show: s.search.show,
//     activeInstance: s.search.activeInstance
//   }));
  
//   const allStocks = useSelector((s) => s.stocks.data);

//   // keep local debounced setter
//   const [localTerm, setLocalTerm] = useState(term);
//   const debouncedSet = useMemo(
//     () => debounce((v) => dispatch(setSearchTerm({ term: v, instanceId })), 300),
//     [dispatch, instanceId]
//   );

//   // compute filtered results
//   const results = useMemo(() => {
//     if (!term || activeInstance !== instanceId) return [];
//     const q = term.toLowerCase();
//     return allStocks
//       .filter(
//         (s) =>
//           s.symbol.toLowerCase().includes(q) ||
//           s.name.toLowerCase().includes(q)
//       )
//       .slice(0, 10);
//   }, [allStocks, term, activeInstance, instanceId]);

//   // clean up debounce on unmount
//   useEffect(() => () => debouncedSet.cancel(), [debouncedSet]);

//   // handlers
//   const onChange = (e) => {
//     setLocalTerm(e.target.value);
//     debouncedSet(e.target.value);
//   };

//   const onIconClick = () => {
//     if (term) {
//       dispatch(setSearchTerm({ term, instanceId }));
//     }
//   };

//   const onSuggestionClick = (symbol) => {
//     dispatch(clearSearch());
//     if(instanceId === "chart"){
//       navigate(`/stocks/${symbol}`)
//     }
//     navigate(`/stocks/${symbol}`);
//   };

//   const onFocus = () => {
//     dispatch(setSearchTerm({ term: localTerm, instanceId }));
//   };

//   return (
//    <div className="relative w-full max-w-md mx-auto">

//       <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2">
//         <input
//           type="text"
//           value={localTerm}
//           onChange={onChange}
//           onFocus={onFocus}
//           className="w-full py-1 px-2 bg-transparent text-gray-900 dark:text-white focus:outline-none"
//           placeholder="Search by name or symbol..."
//         />
//         <button onClick={onIconClick} className="text-gray-600 dark:text-gray-300">
//           <FontAwesomeIcon icon={faSearch} />
//         </button>
//       </div>

//       {show && activeInstance === instanceId && results.length > 0 && (
//         <ul className="absolute top-10 left-0 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md z-50">
//           {results.map((s) => (
//             <li
//               key={s.symbol}
//               onClick={() => onSuggestionClick(s.symbol)}
//               className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-900 dark:text-white"
//             >
//               {s.name} ({s.symbol})
//             </li>
//           ))}
//         </ul>
//       )}
//     </div>
//   );
// }
// src/components/SearchBox.jsx
// import React, { useState, useMemo, useEffect, useCallback } from "react";
// import { useDispatch, useSelector, shallowEqual } from "react-redux";
// import { setSearchTerm, clearSearch } from "../../redux/search/searchSlice";
// import { useNavigate } from "react-router-dom";
// import debounce from "lodash.debounce";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faSearch } from "@fortawesome/free-solid-svg-icons";

// function SearchBox({ instanceId }) {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();

//   // Use separate selectors for stability
//   const term = useSelector((state) => state.search.term);
//   const show = useSelector((state) => state.search.show);
//   const activeInstance = useSelector((state) => state.search.activeInstance);
//   const allStocks = useSelector((state) => state.stocks.data, shallowEqual);

//   const [localTerm, setLocalTerm] = useState(term);

//   // Debounced Redux updater
//   const debouncedSet = useMemo(() =>
//     debounce((v) => {
//       dispatch(setSearchTerm({ term: v, instanceId }));
//     }, 300), [dispatch, instanceId]);

//   useEffect(() => {
//     return () => debouncedSet.cancel();
//   }, [debouncedSet]);

//   // Memoize filtered results
//   const results = useMemo(() => {
//     if (!term || activeInstance !== instanceId) return [];
//     const query = term.toLowerCase();
//     return allStocks
//       .filter(
//         (s) =>
//           s.symbol.toLowerCase().includes(query) ||
//           s.name.toLowerCase().includes(query)
//       )
//       .slice(0, 10);
//   }, [allStocks, term, activeInstance, instanceId]);

//   // Handlers
//   const onChange = useCallback((e) => {
//     const value = e.target.value;
//     setLocalTerm(value);
//     debouncedSet(value);
//   }, [debouncedSet]);

//   const onIconClick = useCallback(() => {
//     if (term) {
//       dispatch(setSearchTerm({ term, instanceId }));
//     }
//   }, [dispatch, term, instanceId]);

//   const onSuggestionClick = useCallback((symbol) => {
//     dispatch(clearSearch());
//     if (instanceId === "chart") {
//       navigate(`/stocks/${symbol}`);
//     } else {
//       navigate(`/stocks/${symbol}`);
//     }
//   }, [dispatch, instanceId, navigate]);

//   const onFocus = useCallback(() => {
//     if (localTerm !== term) {
//       dispatch(setSearchTerm({ term: localTerm, instanceId }));
//     }
//   }, [dispatch, localTerm, term, instanceId]);

//   return (
//     <div className="relative w-full max-w-md mx-auto">
//       <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2">
//         <input
//           type="text"
//           value={localTerm}
//           onChange={onChange}
//           onFocus={onFocus}
//           className="w-full py-1 px-2 bg-transparent text-gray-900 dark:text-white focus:outline-none"
//           placeholder="Search by name or symbol..."
//         />
//         <button onClick={onIconClick} className="text-gray-600 dark:text-gray-300">
//           <FontAwesomeIcon icon={faSearch} />
//         </button>
//       </div>

//       {show && activeInstance === instanceId && results.length > 0 && (
//         <ul className="absolute top-10 left-0 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md z-50">
//           {results.map((s) => (
//             <li
//               key={s.symbol}
//               onClick={() => onSuggestionClick(s.symbol)}
//               className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-900 dark:text-white"
//             >
//               {s.name} ({s.symbol})
//             </li>
//           ))}
//         </ul>
//       )}
//     </div>
//   );
// }

// // Prevent re-renders unless props change
// export default React.memo(SearchBox);
import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { useDispatch, useSelector, shallowEqual } from "react-redux";
import { setSearchTerm, clearSearch, hideSuggestions } from "../../redux/search/searchSlice";
import { useNavigate } from "react-router-dom";
import debounce from "lodash.debounce";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch } from "@fortawesome/free-solid-svg-icons";

function SearchBox({ searchContext }) {
  console.log(searchContext)
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Selectors
  const term = useSelector((state) => state.search.term);
  const show = useSelector((state) => state.search.show);
  const activeInstance = useSelector((state) => state.search.activeInstance);
  const allStocks = useSelector((state) => state.stocks.data, shallowEqual);

  const [localTerm, setLocalTerm] = useState(term);
  const containerRef = useRef(null);

  // Debounced Redux updater with debug logging
  const debouncedSet = useMemo(() =>
    debounce((value) => {
      console.debug(`[SearchBox:${searchContext}] dispatching setSearchTerm:`, value);
      dispatch(setSearchTerm({ term: value, instanceId: searchContext }));
    }, 300), [dispatch, searchContext]);

  useEffect(() => {
    return () => {
      debouncedSet.cancel();
    };
  }, [debouncedSet]);

  // Filter results memoized
  const results = useMemo(() => {
    if (!term || activeInstance !== searchContext) return [];
    const query = term.toLowerCase();
    return allStocks
      .filter(
        (s) =>
          s.symbol.toLowerCase().includes(query) ||
          s.name.toLowerCase().includes(query)
      )
      .slice(0, 10);
  }, [allStocks, term, activeInstance, searchContext]);

  // Handle input change with debug
  const onChange = useCallback((e) => {
    const value = e.target.value;
    console.debug(`[SearchBox:${searchContext}] input changed:`, value);
    setLocalTerm(value);
    debouncedSet(value);
  }, [debouncedSet, searchContext]);

  // Icon click handler
  const onIconClick = useCallback(() => {
    if (term) {
      console.debug(`[SearchBox:${searchContext}] search icon clicked with term:`, term);
      dispatch(setSearchTerm({ term, instanceId: searchContext }));
    }
  }, [dispatch, term, searchContext]);

  // Suggestion click with multiple navigation paths and debug logs
  const onSuggestionClick = useCallback((symbol) => {
    console.debug(`[SearchBox:${searchContext}] suggestion clicked:`, symbol);
    dispatch(clearSearch());

    switch (searchContext) {
      case "chart":
        navigate(`/chart/${symbol}`);
        break;
      case "header":
        navigate(`/stocks/${symbol}`);
        break;
      case "homepage":
        navigate(`/stocks/${symbol}`);
        break;
      default:
        navigate(`/stocks/${symbol}`);
    }
  }, [dispatch, searchContext, navigate]);

  // Sync localTerm and Redux term on focus
  const onFocus = useCallback(() => {
    if (localTerm !== term) {
      console.debug(`[SearchBox:${searchContext}] input focused, syncing term:`, localTerm);
      dispatch(setSearchTerm({ term: localTerm, instanceId: searchContext }));
    }
  }, [dispatch, localTerm, term, searchContext]);

  // Close suggestions on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        if (show && activeInstance === searchContext) {
          console.debug(`[SearchBox:${searchContext}] outside click detected, hiding suggestions`);
          dispatch(hideSuggestions());
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dispatch, show, activeInstance, searchContext]);

  return (
    <div ref={containerRef} className="relative w-full max-w-md mx-auto">
      <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2">
        <input
          type="text"
          value={localTerm}
          onChange={onChange}
          onFocus={onFocus}
          className="w-full py-1 px-2 bg-transparent text-gray-900 dark:text-white focus:outline-none"
          placeholder="Search by name or symbol..."
          autoComplete="off"
        />
        <button onClick={onIconClick} className="text-gray-600 dark:text-gray-300" aria-label="Search">
          <FontAwesomeIcon icon={faSearch} />
        </button>
      </div>

      {show && activeInstance === searchContext && results.length > 0 && (
        <ul className="absolute top-10 left-0 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md z-50 max-h-60 overflow-auto">
          {results.map((s) => (
            <li
              key={s.symbol}
              onClick={() => onSuggestionClick(s.symbol)}
              className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-900 dark:text-white"
            >
              {s.name} ({s.symbol})
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default React.memo(SearchBox);
