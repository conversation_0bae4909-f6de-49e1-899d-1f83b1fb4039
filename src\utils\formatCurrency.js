/**
 * Utility functions for formatting currency values in SAR
 */

/**
 * Format a number as SAR currency
 * @param {number} value - The numeric value to format
 * @param {object} options - Formatting options
 * @param {number} options.minimumFractionDigits - Minimum decimal places (default: 2)
 * @param {number} options.maximumFractionDigits - Maximum decimal places (default: 2)
 * @param {boolean} options.showCurrency - Whether to show "SAR" prefix (default: true)
 * @returns {string} Formatted currency string
 */
export const formatSAR = (value, options = {}) => {
  const {
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showCurrency = true,
  } = options;

  if (value === null || value === undefined || isNaN(value)) {
    return showCurrency ? "SAR 0.00" : "0.00";
  }

  const formattedNumber = Number(value).toLocaleString("en-US", {
    minimumFractionDigits,
    maximumFractionDigits,
  });

  return showCurrency ? `SAR ${formattedNumber}` : formattedNumber;
};

/**
 * Format a number as SAR currency with compact notation for large numbers
 * @param {number} value - The numeric value to format
 * @param {boolean} showCurrency - Whether to show "SAR" prefix (default: true)
 * @returns {string} Formatted currency string with compact notation
 */
export const formatSARCompact = (value, showCurrency = true) => {
  if (value === null || value === undefined || isNaN(value)) {
    return showCurrency ? "SAR 0.00" : "0.00";
  }

  const absValue = Math.abs(value);
  let formattedValue;
  let suffix = "";

  if (absValue >= 1e9) {
    formattedValue = (value / 1e9).toFixed(2);
    suffix = "B";
  } else if (absValue >= 1e6) {
    formattedValue = (value / 1e6).toFixed(2);
    suffix = "M";
  } else if (absValue >= 1e3) {
    formattedValue = (value / 1e3).toFixed(2);
    suffix = "K";
  } else {
    formattedValue = value.toFixed(2);
  }

  const result = `${formattedValue}${suffix}`;
  return showCurrency ? `SAR ${result}` : result;
};

/**
 * Format a percentage change with proper color coding
 * @param {number} value - The percentage value
 * @param {boolean} includeSign - Whether to include + or - sign (default: true)
 * @returns {object} Object with formatted value and CSS class
 */
export const formatPercentageChange = (value, includeSign = true) => {
  if (value === null || value === undefined || isNaN(value)) {
    return {
      value: "0.00%",
      className: "text-gray-500",
      isPositive: false,
    };
  }

  const isPositive = value >= 0;
  const formattedValue = Math.abs(value).toFixed(2);
  const sign = includeSign ? (isPositive ? "+" : "-") : "";
  
  return {
    value: `${sign}${formattedValue}%`,
    className: isPositive 
      ? "text-green-600 dark:text-green-400" 
      : "text-red-600 dark:text-red-400",
    isPositive,
  };
};

/**
 * Format a price change in SAR with proper color coding
 * @param {number} value - The price change value
 * @param {boolean} includeSign - Whether to include + or - sign (default: true)
 * @param {boolean} showCurrency - Whether to show "SAR" prefix (default: true)
 * @returns {object} Object with formatted value and CSS class
 */
export const formatPriceChange = (value, includeSign = true, showCurrency = true) => {
  if (value === null || value === undefined || isNaN(value)) {
    return {
      value: showCurrency ? "SAR 0.00" : "0.00",
      className: "text-gray-500",
      isPositive: false,
    };
  }

  const isPositive = value >= 0;
  const formattedValue = Math.abs(value).toFixed(2);
  const sign = includeSign ? (isPositive ? "+" : "-") : "";
  const currency = showCurrency ? "SAR " : "";
  
  return {
    value: `${sign}${currency}${formattedValue}`,
    className: isPositive 
      ? "text-green-600 dark:text-green-400" 
      : "text-red-600 dark:text-red-400",
    isPositive,
  };
};
