import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Card, Button, Row, Col, Input, Spin } from "antd";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { fetchStocks } from "../../redux/stocks/stocksActions";
import {
  fetchAllPortfolios,
  fetchStockDetails,
  fetchPortfolioHoldings,
} from "../../redux/portfolio/portfolioSlice";
import PortfolioModal from "./PortfolioModal";
import WatchlistPortfolioTabs from "./WatchlistPortfolioTabs";
import { TrendingUp } from "lucide-react";

const VirtualPortfolio = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [topMovers, setTopMovers] = useState([]);
  const [isLoadingTopMovers, setIsLoadingTopMovers] = useState(false);
  const [topMoversError, setTopMoversError] = useState(null);
  const [portfolioHoldingsData, setPortfolioHoldingsData] = useState({});
  const [portfolioValues, setPortfolioValues] = useState({});
  const [portfolioGains, setPortfolioGains] = useState({});

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    portfolios,
    loading: portfoliosLoading,
    stockDetails,
    loadingStockDetails,
  } = useSelector((state) => state.portfolio);
  const { data: stocks } = useSelector((state) => state.stocks);

  useEffect(() => {
    dispatch(fetchStocks());
    dispatch(fetchAllPortfolios());
  }, [dispatch]);

  // Debug logging to understand data structure
  useEffect(() => {
    if (portfolios && portfolios.length > 0) {
      console.log("Portfolios data:", portfolios);
      console.log("First portfolio holdings:", portfolios[0]?.holdings);
    }
  }, [portfolios]);

  useEffect(() => {
    if (stocks && stocks.length > 0) {
      console.log("Stocks data sample:", stocks.slice(0, 2));
    }
  }, [stocks]);

  // Fetch detailed holdings for all portfolios to get real values and top movers
  useEffect(() => {
    if (portfolios && portfolios.length > 0) {
      dispatch(fetchStockDetails(portfolios[0]?.id));
      fetchAllPortfolioHoldings();
    }
  }, [portfolios, dispatch]);

  const fetchAllPortfolioHoldings = async () => {
    if (!portfolios || portfolios.length === 0) return;

    try {
      setIsLoadingTopMovers(true);
      const holdingsData = {};
      const valuesData = {};
      const gainsData = {};
      let allHoldings = [];

      // Fetch holdings for each portfolio
      for (const portfolio of portfolios) {
        try {
          const result = await dispatch(
            fetchPortfolioHoldings({
              portfolioId: portfolio.id,
              gainType: "totalGain",
            })
          ).unwrap();

          if (result.success && result.holdings) {
            holdingsData[portfolio.id] = result.holdings;

            // Calculate portfolio value
            const portfolioValue = result.holdings.reduce((sum, holding) => {
              const quantity = holding.quantity || holding.total_quantity || 0;
              const currentPrice = holding.current_price || 0;
              return sum + quantity * currentPrice;
            }, 0);

            valuesData[portfolio.id] = portfolioValue;

            // Calculate portfolio day gain percentage
            const totalDayGain = result.holdings.reduce((sum, holding) => {
              return sum + (holding.day_gain || 0);
            }, 0);

            const dayGainPercent =
              portfolioValue > 0
                ? (totalDayGain / (portfolioValue - totalDayGain)) * 100
                : 0;

            gainsData[portfolio.id] = {
              dayGain: totalDayGain,
              dayGainPercent: dayGainPercent,
            };

            // Add holdings to all holdings for top movers calculation
            allHoldings = [...allHoldings, ...result.holdings];
          }
        } catch (error) {
          console.error(
            `Error fetching holdings for portfolio ${portfolio.id}:`,
            error
          );
        }
      }

      setPortfolioHoldingsData(holdingsData);
      setPortfolioValues(valuesData);
      setPortfolioGains(gainsData);

      // Calculate top movers from actual holdings
      calculateTopMoversFromHoldings(allHoldings);
    } catch (error) {
      console.error("Error fetching portfolio holdings:", error);
      setTopMoversError("Failed to load portfolio data");
    } finally {
      setIsLoadingTopMovers(false);
    }
  };

  const calculateTopMoversFromHoldings = (allHoldings) => {
    if (!allHoldings || allHoldings.length === 0) {
      setTopMovers([]);
      return;
    }

    // Group holdings by symbol and calculate total gains
    const stockMap = {};
    allHoldings.forEach((holding) => {
      const symbol = holding.symbol;
      if (!stockMap[symbol]) {
        stockMap[symbol] = {
          symbol: symbol,
          name: holding.name || symbol,
          stockPrice: holding.current_price || 0,
          priceChange1D: holding.day_gain || 0,
          totalQuantity: 0,
          totalValue: 0,
        };
      }

      const quantity = holding.quantity || holding.total_quantity || 0;
      const currentPrice = holding.current_price || 0;

      stockMap[symbol].totalQuantity += quantity;
      stockMap[symbol].totalValue += quantity * currentPrice;

      // Use the latest price and day gain
      if (currentPrice > 0) {
        stockMap[symbol].stockPrice = currentPrice;
      }
      if (holding.day_gain !== undefined) {
        stockMap[symbol].priceChange1D = holding.day_gain;
      }
    });

    // Convert to array and sort by absolute day gain
    const movers = Object.values(stockMap)
      .filter((stock) => stock.totalQuantity > 0) // Only include stocks we actually hold
      .sort(
        (a, b) =>
          Math.abs(b.priceChange1D || 0) - Math.abs(a.priceChange1D || 0)
      )
      .slice(0, 5);

    setTopMovers(movers);
  };

  // Log stock details when they change
  // useEffect(() => {
  //   if (stockDetails && Object.keys(stockDetails).length > 0) {
  //     console.log("Stock details from Redux:", stockDetails["1010"]);
  //   }
  // }, [stockDetails]);

  // Calculate total value across all portfolios
  const totalValue = Object.values(portfolioValues).reduce(
    (sum, value) => sum + value,
    0
  );

  // Note: Basic portfolio data doesn't include current prices
  // Total value calculation requires detailed holdings data

  const handlePortfolioClick = (portfolioId) => {
    navigate(`/portfolio/${portfolioId}`);
  };

  const handleStockClick = (symbol) => {
    navigate(`/stocks/${symbol}`);
    setShowSearchResults(false);
    setSearchTerm("");
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setShowSearchResults(value.length > 0);
  };

  const handleSearchFocus = () => {
    if (searchTerm) {
      setShowSearchResults(true);
    }
  };

  // Filter stocks based on search term
  const filteredStocks = searchTerm
    ? stocks
        .filter(
          (stock) =>
            stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
            stock.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .map((stock) => {
          // Enrich stock data with price and day_gain_percent if available
          if (stockDetails[stock.symbol]) {
            return {
              ...stock,
              price: stockDetails[stock.symbol].price,
              day_gain_percent: stockDetails[stock.symbol].day_gain_percent,
            };
          }
          return stock;
        })
    : [];

  // console.log(JSON.stringify(filteredStocks, null, 2));

  return (
    <div className="p-4 bg-white dark:bg-gray-900 min-h-screen">
      {/* Finance Style Search Bar */}
      <div className="mb-8 relative">
        <div className="max-w-xl mx-auto">
          <div className="relative">
            <Input
              placeholder="Search for stocks, by name or symbol"
              className="w-full py-2 px-4 pr-10 bg-white dark:!bg-gray-800 border border-gray-300 dark:!border-gray-600 rounded-lg shadow-sm text-gray-900 dark:!text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={handleSearchChange}
              onFocus={handleSearchFocus}
              size="large"
              suffix={
                <SearchOutlined className="text-gray-500 dark:text-gray-400" />
              }
              style={{ height: "48px" }}
            />

            <style>{`
              .ant-input::placeholder {
                color: #9ca3af !important;
              }
              
              .dark .ant-input::placeholder {
                color: #9ca3af !important;
              }
              
              .custom-table .ant-table {
                background: transparent !important;
              }
              
              .custom-table .ant-table-thead > tr > th {
                background-color: #f9fafb !important;
                color: #4b5563 !important;
                font-weight: 600;
              }
              
              .dark .custom-table .ant-table-thead > tr > th {
                background-color: #1f2937 !important;
                color: #e5e7eb !important;
                border-bottom: 1px solid #374151 !important;
              }
              
              .custom-table .ant-table-tbody > tr > td {
                border-bottom: 1px solid #e5e7eb;
              }
              
              .dark .custom-table .ant-table-tbody > tr > td {
                border-bottom: 1px solid #374151;
              }
              
              .dark .custom-table .ant-table-tbody > tr:hover > td {
                background-color: #374151 !important;
              }
              
              .custom-tabs .ant-tabs-nav::before {
                border-bottom: 1px solid #e5e7eb !important;
              }
              
              .dark .custom-tabs .ant-tabs-nav::before {
                border-bottom: 1px solid #374151 !important;
              }
              
              .custom-tabs .ant-tabs-tab {
                color: #6b7280 !important;
                padding: 12px 16px !important;
                margin: 0 !important;
              }
              
              .custom-tabs .ant-tabs-nav-list {
                gap: 0 !important;
              }
              
              .dark .custom-tabs .ant-tabs-tab {
                color: #9ca3af !important;
              }
              
              .custom-tabs .ant-tabs-tab-active {
                font-weight: 600 !important;
              }
              
              .custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
                color: #2563eb !important;
              }
              
              .dark .custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
                color: #3b82f6 !important;
              }
              
              .custom-tabs .ant-tabs-ink-bar {
                background: #2563eb !important;
                height: 3px !important;
              }
              
              .dark .custom-tabs .ant-tabs-ink-bar {
                background: #3b82f6 !important;
              }
            `}</style>

            {showSearchResults && (
              <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 mt-1">
                <div className="border-b border-gray-200 dark:border-gray-700"></div>

                <div className="max-h-80 overflow-y-auto">
                  {filteredStocks.length > 0 ? (
                    filteredStocks.slice(0, 5).map((stock) => (
                      <div
                        key={stock.symbol}
                        className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                        onClick={() => handleStockClick(stock.symbol)}
                      >
                        <div className="flex justify-between items-center px-4 py-3">
                          <div>
                            <div className="font-medium text-gray-800 dark:text-white">
                              {stock.name}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {stock.symbol} : {stock.exchange || "Tadawul"} (
                              {stock.country || "Saudi Arabia"})
                            </div>
                          </div>
                          <div>
                            <div
                              className={`text-right font-medium ${
                                // For Saudi market: red for positive, green for negative
                                parseFloat(
                                  stock.day_gain_percent ||
                                    stock.priceChange1D ||
                                    0
                                ) >= 0
                                  ? "text-red-600 dark:text-red-400"
                                  : "text-green-600 dark:text-green-400"
                              }`}
                            >
                              {parseFloat(
                                stock.day_gain_percent ||
                                  stock.priceChange1D ||
                                  0
                              ) >= 0
                                ? "▼"
                                : "▲"}{" "}
                              {stock.price ? `${stock.price} SAR ` : ""}
                              {stock.day_gain_percent
                                ? `(${Math.abs(
                                    parseFloat(stock.day_gain_percent)
                                  ).toFixed(2)}%)`
                                : stock.priceChange1D
                                ? `(${Math.abs(
                                    stock.priceChange1D || 0
                                  )?.toFixed(2)}%)`
                                : ""}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-600 dark:text-gray-300">
                      No results found
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <Row gutter={[24, 24]}>
        {/* Portfolio Card - Will appear first on mobile */}
        <Col xs={{ span: 24, order: 1 }} lg={{ span: 8, order: 2 }}>
          {/* Portfolio Card */}
          <Card
            className="border dark:!border-gray-700 dark:!bg-gray-800 mb-8 shadow-sm"
            style={{ borderColor: "#e5e7eb" }}
            styles={{ body: { padding: "16px" } }}
          >
            <div className="flex items-center mb-3">
              <div className="!bg-blue-100 dark:!bg-blue-900 rounded-lg p-2 mr-3">
                <span className="!text-blue-600 dark:!text-blue-300 text-base">
                  📊
                </span>
              </div>
              <div>
                <h3 className="text-base font-semibold text-gray-900 dark:text-white">
                  Your portfolios
                </h3>
                <p className="text-xs text-gray-500 dark:!text-gray-400">
                  Only you can see this
                </p>
              </div>
            </div>

            <div className="my-4 border-t border-b border-gray-200 dark:border-gray-700 py-3">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                Total value
              </p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">
                {totalValue > 0
                  ? `SAR ${totalValue.toFixed(2)}`
                  : portfolios && portfolios.length > 0
                  ? "Loading..."
                  : "SAR 0.00"}
              </p>
            </div>

            {portfoliosLoading ? (
              <div className="flex justify-center py-4">
                <Spin size="default" />
                <span className="ml-2 text-gray-600 dark:text-gray-400">
                  Loading portfolios...
                </span>
              </div>
            ) : portfolios.length === 0 ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                No portfolios yet. Create your first portfolio!
              </div>
            ) : (
              portfolios.map((portfolio) => (
                <div
                  key={portfolio.id}
                  className="flex justify-between items-center p-2 my-2 hover:!bg-gray-50 dark:hover:!bg-gray-700 rounded-lg cursor-pointer bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:!border-gray-600"
                  onClick={() => handlePortfolioClick(portfolio.id)}
                >
                  <span className="text-gray-900 dark:!text-white font-medium text-sm">
                    {portfolio.name}
                  </span>
                  <div className="text-right">
                    <div className="text-gray-900 dark:!text-white font-medium text-sm">
                      {portfolioValues[portfolio.id] !== undefined
                        ? `SAR ${portfolioValues[portfolio.id].toFixed(2)}`
                        : portfolio.holdings &&
                          Array.isArray(portfolio.holdings) &&
                          portfolio.holdings.length > 0
                        ? "Loading..."
                        : "SAR 0.00"}
                    </div>
                    <span
                      className={`text-xs font-medium ${
                        portfolioGains[portfolio.id]?.dayGainPercent !==
                        undefined
                          ? portfolioGains[portfolio.id].dayGainPercent >= 0
                            ? "text-green-500 dark:!text-green-400"
                            : "text-red-500 dark:!text-red-400"
                          : "text-gray-500 dark:!text-gray-400"
                      }`}
                    >
                      {portfolioGains[portfolio.id]?.dayGainPercent !==
                      undefined
                        ? `${
                            portfolioGains[portfolio.id].dayGainPercent >= 0
                              ? "↑"
                              : "↓"
                          } ${Math.abs(
                            portfolioGains[portfolio.id].dayGainPercent
                          ).toFixed(2)}%`
                        : "—"}
                    </span>
                  </div>
                </div>
              ))
            )}

            <Button
              type="default"
              icon={<PlusOutlined />}
              block
              className="mt-4 flex items-center justify-center bg-blue-50 dark:!bg-blue-900 dark:!text-blue-100 !text-blue-600 !border-blue-200 dark:!border-blue-800 hover:!bg-blue-100 dark:hover:!bg-blue-800 font-medium text-sm h-9"
              onClick={() => setIsModalOpen(true)}
            >
              New portfolio
            </Button>
          </Card>
        </Col>

        {/* Top Movers and Lists - Will appear second on mobile */}
        <Col xs={{ span: 24, order: 2 }} lg={{ span: 16, order: 1 }}>
          {/* Top Movers Section */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
              <TrendingUp size={18} className="mr-2 text-blue-500" />
              Top movers in your lists
              {isLoadingTopMovers && <Spin size="small" className="ml-2" />}
            </h2>

            {topMoversError ? (
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <p className="text-red-600 dark:text-red-400 text-sm">
                  {topMoversError}
                </p>
                <button
                  onClick={fetchAllPortfolioHoldings}
                  className="mt-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Try again
                </button>
              </div>
            ) : (
              <div className="space-y-1">
                {topMovers.length > 0 ? (
                  topMovers.map((stock) => (
                    <div
                      key={stock.symbol}
                      className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                    >
                      <div className="flex items-center">
                        <div className="bg-blue-600 text-white rounded-md p-1.5 mr-3 w-12 text-center font-medium text-xs">
                          {stock.symbol}
                        </div>
                        <span className="text-gray-900 dark:text-white font-medium text-sm">
                          {stock.name}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className="text-gray-900 dark:text-white font-medium text-sm">
                          {stock.stockPrice
                            ? `SAR ${stock.stockPrice.toFixed(2)}`
                            : "N/A"}
                        </span>
                        <span
                          className={`font-medium text-sm ${
                            (stock.priceChange1D || 0) >= 0
                              ? "text-green-600 dark:text-green-400"
                              : "text-red-600 dark:text-red-400"
                          }`}
                        >
                          {(stock.priceChange1D || 0) >= 0 ? "+" : ""}SAR{" "}
                          {Math.abs(stock.priceChange1D || 0).toFixed(2)}
                        </span>
                        <span
                          className={`font-medium text-sm inline-flex items-center px-2 py-1 rounded ${
                            (stock.priceChange1D || 0) >= 0
                              ? "text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/20"
                              : "text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/20"
                          }`}
                        >
                          {(stock.priceChange1D || 0) >= 0 ? "↑" : "↓"}{" "}
                          {Math.abs(stock.priceChange1D || 0).toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    No holdings found in your portfolios
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Your Lists Section */}
          <div className="mb-6">
            <h3 className="text-base font-semibold mb-3 text-gray-900 dark:text-white flex items-center">
              YOUR LISTS
            </h3>
            <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm">
              <WatchlistPortfolioTabs
                onCreatePortfolio={() => setIsModalOpen(true)}
              />
            </div>
          </div>
        </Col>
      </Row>

      <PortfolioModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default VirtualPortfolio;
