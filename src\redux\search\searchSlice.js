// src/redux/search/searchSlice.js
import { createSlice } from "@reduxjs/toolkit";

const searchSlice = createSlice({
  name: "search",
  initialState: {
    term: "",
    show: false,
    activeInstance: null // Track which search box is active
  },
  reducers: {
    setSearchTerm(state, action) {
      state.term = action.payload.term;
      state.show = true;
      state.activeInstance = action.payload.instanceId;
    },
    clearSearch(state) {
      state.term = "";
      state.show = false;
      state.activeInstance = null;
    },
    hideSuggestions(state) {
      state.show = false;
      state.activeInstance = null;
    },
  },
});

export const { setSearchTerm, clearSearch, hideSuggestions } = searchSlice.actions;
export default searchSlice.reducer;