# Stage 1: Build the frontend application
FROM node:20 as build-stage
WORKDIR /app

# Declare a build argument for the backend URL
ARG VITE_BACKEND_URL

# Set it as an environment variable for the build process
ENV VITE_BACKEND_URL=${VITE_BACKEND_URL}

COPY package*.json ./
RUN npm install
COPY . .

# Now, when vite build runs, it will have VITE_BACKEND_URL available
RUN node --max-old-space-size=8192 node_modules/vite/bin/vite.js build

# Stage 2: Serve the static files with Nginx
FROM nginx:alpine

COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
