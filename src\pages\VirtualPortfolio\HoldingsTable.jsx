import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";
import { ChevronDown, ChevronUp, Plus, Trash2, Trash } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { fetchPortfolioHoldings, deletePurchaseRecordAsync } from "../../redux/portfolio/portfolioSlice";
const HoldingsTable = ({
  holdings,
  onDeleteStock,
  portfolioName,
  onRenamePortfolio,
  onDeletePortfolio,
  loading,
  gainType,
  portfolioId,
  onGainTypeChange,
  onOpenAddInvestmentModal,
}) => {
  const [currentGainType, setCurrentGainType] = useState(
    gainType || "totalGain"
  );
  const [selectedStockForPurchase, setSelectedStockForPurchase] =
    useState(null);
  const [isGainDropdownOpen, setIsGainDropdownOpen] = useState(false);
  const dispatch = useDispatch();
  const gainDropdownRef = useRef(null);

  // Update currentGainType when gainType prop changes
  useEffect(() => {
    setCurrentGainType(gainType);
  }, [gainType]);

  const handleGainTypeChange = async (newType) => {
    try {
      setCurrentGainType(newType);
      setIsGainDropdownOpen(false);

      // Use parent's handler if provided, otherwise use local logic
      if (onGainTypeChange) {
        onGainTypeChange(newType);
      } else {
        const toastId = toast.loading("Fetching holdings...");
        await dispatch(
          fetchPortfolioHoldings({
            portfolioId,
            gainType: newType,
          })
        );
        toast.update(toastId, {
          render: `Switched to ${newType === "totalGain" ? "Total" : "Day"
            } Gain view`,
          type: "success",
          isLoading: false,
          autoClose: 2000,
        });
      }
    } catch (error) {
      toast.error(
        `Failed to fetch ${newType === "totalGain" ? "Total" : "Day"} Gain data`
      );
    }
  };

  const [sortConfig, setSortConfig] = useState({
    key: "symbol",
    direction: "asc",
  });
  const [newPortfolioName, setNewPortfolioName] = useState(portfolioName);
  const [isRenaming, setIsRenaming] = useState(false);
  const [expandedSymbol, setExpandedSymbol] = useState(null);
  const [activeMenu, setActiveMenu] = useState(null);
  const menuRef = useRef(null);
  
  // Function to handle recording a purchase for a specific stock
  const handleRecordPurchase = (symbol) => {
    // Close any open menus
    setActiveMenu(null);
    
    // Find the stock details in the holdings
    const stock = holdings.find(h => h.symbol === symbol);
    
    if (stock) {
      // Set the selected stock for purchase
      setSelectedStockForPurchase({
        symbol: stock.symbol,
        name: stock.name,
        currentPrice: stock.current_price
      });
      
      // Call the parent component's function to open the AddInvestmentModal
      if (onOpenAddInvestmentModal) {
        onOpenAddInvestmentModal(symbol);
      } else {
        console.error('onOpenAddInvestmentModal prop is not provided to HoldingsTable');
      }
    }
  };

  // Get token from user state
  // const { userInfo } = useSelector((state) => state.user);
  // console.log(userInfo.token)
  // Use environment variable directly for backendURL
  const backendURL =
    import.meta.env.VITE_BACKEND_URL || "http://localhost:3000/api";

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setActiveMenu(null);
      }
      if (
        gainDropdownRef.current &&
        !gainDropdownRef.current.contains(event.target)
      ) {
        setIsGainDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Validate holding data to prevent rendering errors
  const validateHolding = (holding) => {
    // Log the raw holding data
    // console.log('Raw holding data:', holding);

    // Create a new object with default values
    const validatedHolding = {
      ...holding,
      // Use nullish coalescing for required fields
      current_price: holding.current_price ?? 0,
      total_quantity: holding.total_quantity ?? 0,
      avg_purchase_price: holding.avg_purchase_price ?? 0,
      // Set the gain type based on the current selection
      gain_type: currentGainType,
      // Keep the original values without modifying them
      value: holding.value ?? 0,
    };

    // For day gain view, use day_gain values if available, otherwise keep total_gain
    if (currentGainType === "dayGain") {
      if (holding.day_gain !== undefined) {
        validatedHolding.total_gain = holding.day_gain;
        validatedHolding.total_gain_percent = holding.day_gain_percent;
      }
    }

    // Log the validated holding data
    // console.log('Validated holding data:', validatedHolding);

    // Log warnings for missing properties
    const errors = [];
    const requiredFields = [
      "current_price",
      "total_quantity",
      "avg_purchase_price",
    ];

    requiredFields.forEach((field) => {
      if (holding[field] === undefined || holding[field] === null) {
        errors.push(field);
        // Set default values for missing fields
        validatedHolding[field] = 0;
      }
    });

    if (errors.length > 0) {
      console.warn(
        `Fixed ${errors.length} issues with holding ${holding.symbol
        }: ${errors.join(", ")}`
      );
    }

    return validatedHolding;
  };

  // Apply validation to all holdings before sorting or rendering
  const validatedHoldings = holdings.map(validateHolding);

  // Handle the new data format from the API
  const sortedHoldings = [...validatedHoldings].sort((a, b) => {
    if (sortConfig.key === "total_gain_percent") {
      return sortConfig.direction === "asc"
        ? a.total_gain_percent - b.total_gain_percent
        : b.total_gain_percent - a.total_gain_percent;
    }

    if (sortConfig.key === "value") {
      return sortConfig.direction === "asc"
        ? a.value - b.value
        : b.value - a.value;
    }

    if (sortConfig.key === "symbol") {
      return sortConfig.direction === "asc"
        ? a.symbol.localeCompare(b.symbol)
        : b.symbol.localeCompare(a.symbol);
    }

    return 0;
  });

  const handleSort = (key) => {
    setSortConfig({
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    });
  };

  const handleRenamePortfolio = () => {
    onRenamePortfolio(newPortfolioName);
    setIsRenaming(false);
  };

  const toggleExpandHolding = (symbol) => {
    if (expandedSymbol === symbol) {
      setExpandedSymbol(null);
    } else {
      setExpandedSymbol(symbol);
    }
  };

  const toggleMenu = (symbol) => {
    setActiveMenu(activeMenu === symbol ? null : symbol);
  };

  // The handleRecordPurchase function has been moved to the top of the component

  const handleDeleteRecord = async (symbol, purchase_id) => {
    // console.log("Attempting to delete record:", {
    //   symbol,
    //   purchase_id,
    //   portfolioId,
    // });

    // if (!userInfo?.token) {
    //   toast.error("You must be logged in to delete records");
    //   console.error("User is not logged in");
    //   return;
    // }

    // If we don't have a valid ID, show an error
    if (purchase_id === undefined || purchase_id === null) {
      console.log("id", purchase_id);
      console.error("Missing holding ID for deletion");
      toast.error("Cannot delete record: Missing ID");
      return;
    }

    try {
      // Show loading toast
      const loadingToastId = toast.loading("Deleting purchase record...");

      // Use the Redux action to delete the purchase record
      // This is more efficient and consistent with the rest of the application
      const resultAction = await dispatch(
        deletePurchaseRecordAsync({
          portfolioId,
          symbol,
          purchase_id,
        })
      );

      // Check if the action was fulfilled or rejected
      if (deletePurchaseRecordAsync.fulfilled.match(resultAction)) {
        // Success case
        toast.update(loadingToastId, {
          render: "Purchase record deleted successfully",
          type: "success",
          isLoading: false,
          autoClose: 3000,
        });

        // Force UI update by clearing expanded symbol
        setExpandedSymbol(null);

        // Re-expand the symbol after a short delay to show updated data
        setTimeout(() => {
          setExpandedSymbol(symbol);
        }, 500);
      } else {
        // Error case - the toast will be shown by the rejected case in the slice
        toast.update(loadingToastId, {
          render: resultAction.payload || "Failed to delete purchase record",
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
      }
    } catch (error) {
      console.error("Error deleting purchase record:", error);
      toast.error(error.message || "Failed to delete purchase record");
    }
  };

  // Update expanded details rendering to match screenshot
  const renderExpandedDetails = (holding) => {
    if (
      !expandedSymbol ||
      expandedSymbol !== holding.symbol ||
      !holding.details
    ) {
      return null;
    }

    // Debug log to see the structure of the details
    // console.log("Expanded details for", holding.symbol, ":", holding.details);

    // Validate details to prevent rendering errors
    const validatedDetails = holding.details.map((detail, index) => {
      const errors = [];
      const validated = { ...detail };

      // Debug log for each detail
      // console.table(validated)
      // console.log(`Detail ${index}:`, detail);

      // Extract the purchase_id for the purchase record based on the backend API response
      let purchase_id;

      // Based on your backend screenshot, the property is named 'purchase_id'
      if (detail.purchase_id !== undefined) {
        purchase_id = detail.purchase_id;
      }

      if (
        validated.purchase_date === undefined ||
        validated.purchase_date === null
      ) {
        console.warn(
          `Detail ${index} for ${holding.symbol} missing purchase_date`
        );
        errors.push("purchase_date");
        validated.purchase_date = new Date().toISOString();
      }

      if (
        validated.purchase_price === undefined ||
        validated.purchase_price === null
      ) {
        console.warn(
          `Detail ${index} for ${holding.symbol} missing purchase_price`
        );
        errors.push("purchase_price");
        validated.purchase_price = 0;
      }

      if (validated.quantity === undefined || validated.quantity === null) {
        console.warn(`Detail ${index} for ${holding.symbol} missing quantity`);
        errors.push("quantity");
        validated.quantity = 0;
      }

      if (validated.total_gain === undefined || validated.total_gain === null) {
        console.warn(
          `Detail ${index} for ${holding.symbol} missing total_gain`
        );
        errors.push("total_gain");
        validated.total_gain = 0;
      }

      if (
        validated.total_gain_percent === undefined ||
        validated.total_gain_percent === null
      ) {
        console.warn(
          `Detail ${index} for ${holding.symbol} missing total_gain_percent`
        );
        errors.push("total_gain_percent");
        validated.total_gain_percent = 0;
      }

      if (validated.value === undefined || validated.value === null) {
        console.warn(`Detail ${index} for ${holding.symbol} missing value`);
        errors.push("value");
        validated.value = 0;
      }

      if (errors.length > 0) {
        console.warn(
          `Fixed ${errors.length} issues with detail ${index} for ${holding.symbol
          }: ${errors.join(", ")}`
        );
      }

      // Store the purchase ID in the validated object
      validated.purchase_id = purchase_id;

      return validated;
    });

    return (
      <tr className="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600">
        <td colSpan="7" className="p-0">
          <div className="px-4 py-3">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Purchase Date
                  </th>
                  <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Purchase Price
                  </th>
                  <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Quantity
                  </th>
                  {/* <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Gain Type
                  </th> */}
                  <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Total Gain
                  </th>
                  <th className="p-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                    Value
                  </th>
                  <th className="p-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400"></th>
                </tr>
              </thead>
              <tbody>
                {validatedDetails.map((detail, index) => {
                  // Debug log for the ID we're using
                  // console.log(`Using ID for delete:`, detail.purchase_id);

                  return (
                    <tr
                      key={`${holding.symbol}-${index}`}
                      className="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <td className="py-3 px-2 text-sm text-gray-600 dark:text-gray-300">
                        {new Date(detail.purchase_date).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-2 text-sm text-gray-600 dark:text-gray-300">
                        SAR&nbsp;{(detail.purchase_price || 0).toFixed(2)}
                      </td>
                      <td className="py-3 px-2 text-sm text-gray-600 dark:text-gray-300">
                        {detail.quantity || 0}
                      </td>
                      <td className="py-3 px-2 text-sm">
                        <div className="flex items-center flex-nowrap">
                          <span
                            className={`${(detail.total_gain || 0) >= 0
                              ? "text-green-500"
                              : "text-red-500"
                              }`}
                          >
                            {(detail.total_gain || 0) >= 0 ? "+" : "-"}SAR&nbsp;
                            {Math.abs(detail.total_gain || 0).toFixed(2)}
                          </span>
                          <span
                            className={`ml-2 ${(detail.total_gain_percent || 0) >= 0
                              ? "text-green-500"
                              : "text-red-500"
                              }`}
                          >
                            (
                            {Math.abs(detail.total_gain_percent || 0).toFixed(
                              2
                            )}
                            %)
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-2 text-sm text-gray-600 dark:text-gray-300">
                        SAR&nbsp;{(detail.value || 0).toFixed(2)}
                      </td>
                      <td className="py-3 px-2 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          {/* <button className="text-gray-500 hover:text-blue-500 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded p-1 h-6 w-6">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="w-4 h-4"
                            >
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button> */}
                          <button
                            className="text-gray-500 hover:text-red-500 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded p-1 h-6 w-6"
                            onClick={() =>
                              handleDeleteRecord(
                                holding.symbol,
                                detail.purchase_id
                              )
                            }
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="w-4 h-4"
                            >
                              <path d="M3 6h18"></path>
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
                <tr className="bg-gray-50 dark:bg-gray-800">
                  <td colSpan="6" className="p-2">
                    <button
                      className="flex items-center text-blue-500 hover:text-blue-700 text-sm font-medium ml-2"
                      onClick={() => handleRecordPurchase(holding.symbol)}
                    >
                      <Plus size={16} className="mr-1" />
                      Record another purchase
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-300">
          Loading holdings...
        </p>
      </div>
    );
  }

  if (holdings.length === 0) {
    return (
      <div className="w-full overflow-x-auto">
        {renderGainTypeButtons()}
        <table className="min-w-full">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                Symbol
              </th>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                Name
              </th>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                Price
              </th>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                Quantity
              </th>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                {gainType === "totalGain" ? "Total Gain" : "Day Gain"}
              </th>
              <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
                Value
              </th>
              <th className="w-24 p-3 text-right dark:text-gray-200"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan="7" className="p-3 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  Nothing in this portfolio yet
                </p>
                <p className="text-gray-500 dark:text-gray-400">
                  Add investments to see performance and track returns
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  return (
    <div className="mb-14">

      <div className="flex justify-between items-center mb-4">
        {isRenaming ? (
          <input
            type="text"
            value={newPortfolioName}
            onChange={(e) => setNewPortfolioName(e.target.value)}
            className="border rounded-lg p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
          />
        ) : (
          <h2 className="text-xl font-semibold dark:text-gray-100">
            {portfolioName}
          </h2>
        )}
        <div className="flex space-x-2">
          {isRenaming ? (
            <>
              <button
                onClick={handleRenamePortfolio}
                className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Save
              </button>
              <button
                onClick={() => setIsRenaming(false)}
                className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              >
                Cancel
              </button>
            </>
          ) : (
            <>
              <button
                onClick={onOpenAddInvestmentModal}
                className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                + Add Investment
              </button>

              <button
                onClick={() => setIsRenaming(true)}
                className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Rename
              </button></>
          )}
          <button
            onClick={onDeletePortfolio}
            className="px-3 py-1 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            Delete Portfolio
          </button>
        </div>
      </div>

      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <th
              onClick={() => handleSort("symbol")}
              className="p-3 text-left cursor-pointer dark:text-gray-200 uppercase text-xs font-medium"
            >
              Symbol
              {/* {sortConfig.key === "symbol" &&
                (sortConfig.direction === "asc" ? " ↑" : " ↓")} */}
            </th>
            <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
              Name
            </th>
            <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
              Price
            </th>
            <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
              Quantity
            </th>
            <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium relative">
              <div className="flex items-center justify-between">
                <span
                  onClick={() => handleSort("total_gain_percent")}
                  className="cursor-pointer flex-1"
                >
                  {currentGainType === "totalGain" ? "TOTAL GAIN" : "DAY GAIN"}
                </span>
                <div className="relative" ref={gainDropdownRef}>
                  <button
                    onClick={() => setIsGainDropdownOpen(!isGainDropdownOpen)}
                    className="ml-2 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded border"
                    title="Change gain type"
                  >
                    <ChevronDown size={14} />
                  </button>
                  {isGainDropdownOpen && (
                    <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border dark:border-gray-600 rounded-md overflow-hidden shadow-lg z-10 min-w-[120px]">
                      <button
                        onClick={() => handleGainTypeChange("totalGain")}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${currentGainType === "totalGain"
                          ? "bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300"
                          : "text-gray-700 dark:text-gray-200"
                          }`}
                      >
                        Total Gain
                      </button>
                      <button
                        onClick={() => handleGainTypeChange("dayGain")}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${currentGainType === "dayGain"
                          ? "bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300"
                          : "text-gray-700 dark:text-gray-200"
                          }`}
                      >
                        Day Gain
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </th>
            <th className="p-3 text-left dark:text-gray-200 uppercase text-xs font-medium">
              Value
            </th>
            <th className="w-24 p-3 text-right dark:text-gray-200"></th>
          </tr>
        </thead>
        <tbody>
          {sortedHoldings.map((holding, index) => (
            <React.Fragment key={holding.symbol + index}>
              <tr className="border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                <td className="p-3 dark:text-gray-200 font-medium">
                  <span className="bg-gray-100 text-gray-800 dark:!bg-gray-700 dark:!text-gray-200 text-xs px-2 py-1 rounded font-bold uppercase">
                    {holding.symbol}
                  </span>
                </td>
                <td className="p-3 dark:text-gray-200">
                  <span className="text-gray-900 dark:text-white">
                    {holding.name ||
                      holding.symbol_code ||
                      holding.symbol ||
                      "Unknown Stock"}
                  </span>
                </td>
                <td className="p-3 dark:text-gray-200">
                  SAR&nbsp;{(holding.current_price || 0).toFixed(2)}
                </td>
                <td className="p-3 dark:text-gray-200">
                  {holding.total_quantity || 0}
                </td>
                <td className="p-3">
                  <div className="flex items-center flex-nowrap">
                    {currentGainType === "dayGain" ? (
                      <>
                        <span
                          className={`${holding.day_gain >= 0
                            ? "text-green-500"
                            : "text-red-500"
                            }`}
                        >
                          {holding.day_gain >= 0 ? "+" : "-"}SAR&nbsp;
                          {Math.abs(holding.day_gain || 0)?.toFixed(2)}
                        </span>
                        <span
                          className={`ml-2 ${holding.day_gain_percent >= 0
                            ? "text-green-500"
                            : "text-red-500"
                            }`}
                        >
                          ({holding.day_gain_percent?.toFixed(2)}%)
                        </span>
                      </>
                    ) : (
                      <>
                        <span
                          className={`${(holding.total_gain || 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                            }`}
                        >
                          {(holding.total_gain || 0) >= 0 ? "+" : "-"}SAR&nbsp;
                          {Math.abs(holding.total_gain || 0)?.toFixed(2)}
                        </span>
                        <span
                          className={`ml-2 ${(holding.total_gain_percent || 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                            }`}
                        >
                          ({holding.total_gain_percent?.toFixed(2) || "0.00"}%)
                        </span>
                      </>
                    )}
                  </div>
                </td>
                <td className="p-3 dark:text-gray-200 font-semibold">
                  SAR&nbsp;{(holding.value || 0).toFixed(2)}
                </td>
                <td className="p-3 text-right relative">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => toggleExpandHolding(holding.symbol)}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded p-1 h-6 w-6 flex items-center justify-center"
                      aria-label="Toggle details"
                    >
                      {expandedSymbol === holding.symbol ? (
                        <ChevronUp size={14} />
                      ) : (
                        <ChevronDown size={14} />
                      )}
                    </button>
                    <button
                      onClick={() => toggleMenu(holding.symbol)}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded p-1 h-6 w-6 flex items-center justify-center"
                      aria-label="More options"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="5" r="1"></circle>
                        <circle cx="12" cy="19" r="1"></circle>
                      </svg>
                    </button>
                    {activeMenu === holding.symbol && (
                      <div
                        ref={menuRef}
                        className="absolute right-0 mt-1 w-48 rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                        style={{ minWidth: "180px", top: "100%" }}
                      >
                        <div className="py-1 divide-y divide-gray-100 dark:divide-gray-700">
                          <button
                            onClick={() => handleRecordPurchase(holding.symbol)}
                            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Plus size={16} className="mr-2" />
                            Record a purchase
                          </button>
                          <div className="divide-y divide-gray-100 dark:divide-gray-700">
                            <button
                              onClick={() => onDeleteStock(holding.symbol)}
                              className="flex w-full items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                              <Trash2 size={16} className="mr-2" />
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
              {renderExpandedDetails(holding)}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

HoldingsTable.propTypes = {
  holdings: PropTypes.array.isRequired,
  onDeleteStock: PropTypes.func.isRequired,
  portfolioName: PropTypes.string.isRequired,
  onRenamePortfolio: PropTypes.func.isRequired,
  onDeletePortfolio: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  gainType: PropTypes.string,
  portfolioId: PropTypes.string.isRequired,
  onGainTypeChange: PropTypes.func,
};

HoldingsTable.defaultProps = {
  loading: false,
  gainType: "totalGain",
};

export default HoldingsTable;
