// src/components/StockDetails/StockDetailsCard.jsx

import React, { useState } from "react";
import PropTypes from "prop-types";
import { Button } from "antd";
import { EditOutlined } from "@ant-design/icons";

import FilterModal from "./FilterModal";

const StockDetailsCard = ({ metrics }) => {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);

  // Get a set of the default metric labels to filter out duplicates
  const defaultLabels = new Set(metrics.map((m) => m.label));

  // Only keep those selectedFilters that aren’t already in defaultLabels
  const uniqueFilters = selectedFilters.filter((f) => !defaultLabels.has(f));

  // Merge original metrics with unique (non‐duplicate) filters
  const combinedMetrics = [
    ...metrics,
    ...uniqueFilters.map((filter) => ({
      id: `custom-${filter}`,
      label: filter,
      value: "N/A",
    })),
  ];

  return (
    <div className="w-full sm:w-2/3 lg:w-1/3 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white">
          Stock Details
        </h2>

        <FilterModal
          visible={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          title="Indicators"
          selectedFilters={selectedFilters}
          onChange={setSelectedFilters}
        />

        <Button
          type="default"
          icon={<EditOutlined />}
          className="border px-3 py-1"
          onClick={() => setIsFilterModalOpen(true)}
        >
          Edit
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
        {combinedMetrics.map(({ id, label, value }) => (
          <div
            key={id}
            className="flex justify-between items-center p-2 border-b border-gray-200 dark:border-gray-700"
          >
            <span className="font-semibold text-gray-900 dark:text-white">
              {label}
            </span>
            <span className="text-gray-600 dark:text-gray-300">
              {value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

StockDetailsCard.propTypes = {
  metrics: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ).isRequired,
};

export default StockDetailsCard;
