const TabBar = ({ selectedTab, setSelectedTab, tabs }) => {
  return (
    <div className="text-sm font-medium text-center text-gray-500 rounded-lg shadow-sm sm:flex dark:divide-gray-700 dark:text-gray-400">
      {tabs.map(({ key, label }) => (
        <div key={key} className="w-full focus-within:z-10">
          <div
            onClick={() => setSelectedTab(key)}
            className={`inline-block w-full p-4 border-r border-gray-200 dark:border-gray-700 rounded-s-lg focus:ring-4 focus:ring-blue-300 active focus:outline-none cursor-pointer ${
              selectedTab === key ? "bg-gray-100 dark:bg-gray-800" : ""
            }`}
          >
            <span
              className={`${
                selectedTab === key
                  ? "underline underline-offset-9 text-black font-bold dark:text-white"
                  : ""
              }`}
            >
              {label}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TabBar;
