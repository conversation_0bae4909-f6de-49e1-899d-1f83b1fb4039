import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchStockScreenerOverview,
  fetchStocksByIndustry,
  fetchFilteredStocks,
  fetchPerformance,
  fetchEarnings
} from './screenerService';

// Thunks
export const loadScreenerOverview    = createAsyncThunk('screener/loadOverview',    fetchStockScreenerOverview);
export const loadStocksByIndustry    = createAsyncThunk('screener/loadByIndustry',  fetchStocksByIndustry);
export const loadFilteredStocks      = createAsyncThunk('screener/loadFiltered',   fetchFilteredStocks);
export const loadPerformance         = createAsyncThunk('screener/loadPerformance', fetchPerformance);
export const loadEarnings            = createAsyncThunk('screener/loadEarnings',    fetchEarnings);

const initialState = {
  overview: [],
  performance: [],
  earnings: [],
  status: 'idle',
  error: null
};

const screenerSlice = createSlice({
  name: 'screener',
  initialState,
  extraReducers: (b) => {
    b
    // Overview
    .addCase(loadScreenerOverview.pending,   s=>{s.status='loading';})
    .addCase(loadScreenerOverview.fulfilled, (s,a)=>{s.status='succeeded'; s.overview=a.payload;})
    .addCase(loadScreenerOverview.rejected,  (s,a)=>{s.status='failed'; s.error=a.error.message;})

    // Industry
    .addCase(loadStocksByIndustry.fulfilled, (s,a)=>{s.status='succeeded'; s.overview=a.payload;})

    // Filter
    .addCase(loadFilteredStocks.fulfilled,   (s,a)=>{s.status='succeeded'; s.overview=a.payload;})

    // Performance
    .addCase(loadPerformance.pending,        s=>{s.status='loading';})
    .addCase(loadPerformance.fulfilled,      (s,a)=>{s.status='succeeded'; s.performance=a.payload;})
    .addCase(loadPerformance.rejected,       (s,a)=>{s.status='failed'; s.error=a.error.message;})

    // Earnings
    .addCase(loadEarnings.pending,           s=>{s.status='loading';})
    .addCase(loadEarnings.fulfilled,         (s,a)=>{s.status='succeeded'; s.earnings=a.payload;})
    .addCase(loadEarnings.rejected,          (s,a)=>{s.status='failed'; s.error=a.error.message;});
  }
});

export default screenerSlice.reducer;
