// import { useParams } from "react-router";
import useTwelveDataWebSocket from '../../../hooks/useTwelveDataWebSocket';

const ChartTab = () => {
//   const { id } = useParams();
// console.log(id)
const id = "BTC/USD"
const apiKey = '5688324e509d4f84b4e03cad2f60bf27'; // ideally from env or config
const { data, status } = useTwelveDataWebSocket(id, apiKey);

if (status === 'connecting') return <div>Connecting...</div>;
if (status === 'error') return <div>Error connecting to WebSocket</div>;
  return (
    <div>
    <h2>{id} Live Price</h2>
    {data ? (
      <div>
        <p>Price: {data.price}</p>
        <p>Timestamp: {new Date(data.timestamp * 1000).toLocaleTimeString()}</p>
        {/* Render your chart component here, feeding live data */}
      </div>
    ) : (
      <p>Waiting for data...</p>
    )}
  </div>
  )
}

export default ChartTab