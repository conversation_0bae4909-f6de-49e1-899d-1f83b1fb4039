// src/redux/stocks/stocksSlice.js
import { createSlice } from "@reduxjs/toolkit";
import {
  fetchStocks,
  fetchStockHeaderBySymbol,
  getTimeSeriesData,
  fetchQuotesBySymbols,
  fetchTasi,
  fetchNumo,
  fetchAllMarket,
  fetchIndustriesList,
  fetchByIndustry,
} from "./stocksActions";

const initialState = {
  data: [],             // drives your main table
  industries: [],
  header: null,         // result of fetchStockHeaderBySymbol
  timeSeriesData: null, // result of getTimeSeriesData
  quotes: [],           // result of fetchQuotesBySymbols
  status: "idle",       // for generic loading/error
  error: null,
};

const stocksSlice = createSlice({
  name: "stocks",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    //
    // 1) Master list
    //
    builder
      .addCase(fetchStocks.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchStocks.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchStocks.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

    //
    // 2) Header details
    //
    builder
      .addCase(fetchStockHeaderBySymbol.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchStockHeaderBySymbol.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.header = action.payload;
      })
      .addCase(fetchStockHeaderBySymbol.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });

    //
    // 3) Time series
    //
    builder
      .addCase(getTimeSeriesData.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(getTimeSeriesData.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.timeSeriesData = action.payload;
      })
      .addCase(getTimeSeriesData.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });

    //
    // 4) Live quotes
    //
    builder
      .addCase(fetchQuotesBySymbols.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchQuotesBySymbols.fulfilled, (state, action) => {
        state.status = "succeeded";
        // merge quotes into data array
        action.payload.forEach((q) => {
          const idx = state.data.findIndex((s) => s.symbol === q.symbol);
          if (idx !== -1) state.data[idx] = { ...state.data[idx], ...q };
        });
        state.quotes = action.payload;
      })
      .addCase(fetchQuotesBySymbols.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });

    //
    // 5) All‑Market (default)
    //
    builder
      .addCase(fetchAllMarket.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchAllMarket.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchAllMarket.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

    //
    // 6) TASI
    //
    builder
      .addCase(fetchTasi.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchTasi.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchTasi.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

    //
    // 7) NUMO
    //
    builder
      .addCase(fetchNumo.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchNumo.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchNumo.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

       // Industries list
       builder
      .addCase(fetchIndustriesList.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchIndustriesList.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.industries = action.payload;
      })
      .addCase(fetchIndustriesList.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      })
      // Filter by industry
      builder
      .addCase(fetchByIndustry.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchByIndustry.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchByIndustry.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

export default stocksSlice.reducer;
