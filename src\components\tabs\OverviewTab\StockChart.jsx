import PropTypes from "prop-types";
import { Line } from "react-chartjs-2";

const StockChart = ({ chartData }) => {
  const { data, options } = chartData;
// console.log(data)
console.log(chartData)
  return (
    <div className="w-full sm:w-3/4 lg:w-2/3 max-w-2xl p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md flex flex-col justify-center items-center">
      {/* Time Range Buttons */}
      <div className="flex flex-wrap gap-2 mb-4 justify-center">
        {chartData.timeRanges.map((range) => (
          <button
            key={range}
            className="text-xs sm:text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition"
          >
            {range}
          </button>
        ))}
      </div>

      {/* Chart Display */}
      <div className="lg:w-full flex items-center justify-center">
        <Line data={data} options={options} />
      </div>

      {/* Stock Price Info */}
      <div className="text-center mt-4 text-xs sm:text-sm text-gray-900 dark:text-white">
        <p>
          Price Open: <strong>{chartData.priceOpen} SAR</strong> | Price Close:{" "}
          <strong>{chartData.priceClose} SAR</strong>
        </p>
        <p>
          Change:{" "}
          <span className="text-red-500">
            {chartData.change} SAR ({chartData.percentChange}%)
          </span>
        </p>
      </div>
    </div>
  );
};

StockChart.propTypes = {
  chartData: PropTypes.shape({
    data: PropTypes.object.isRequired,
    options: PropTypes.object.isRequired,
    timeRanges: PropTypes.arrayOf(PropTypes.string).isRequired,
    priceOpen: PropTypes.string.isRequired,
    priceClose: PropTypes.string.isRequired,
    change: PropTypes.string.isRequired,
    percentChange: PropTypes.string.isRequired,
  }).isRequired,
};

export default StockChart;