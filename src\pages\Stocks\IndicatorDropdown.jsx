import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown, faTimes } from "@fortawesome/free-solid-svg-icons";

const indicatorsList = [
  "Symbol", "Company Name", "Market Cap", "Enterprise Value", "PE Ratio",
  "Forward PE", "PS Ratio", "PB Ratio", "P/FCF Ratio", "Industry", "Sector",
  "Stock Price", "Exchange", "Dividend Yield", "Dividend Per Share ($)",
  "Last Dividend ($)", "Dividend Growth", "Dividend Growth Years",
  "Dividend Annual Growth (5Y)", "Price Change 1D (%)", "Price Change 1W",
  "Price Change 1M", "Price Change 3M", "Price Change 6M", "Price Change YTD",
  "Price Change 1Y", "Total Return 1W", "Total Return 1M", "Total Return 3M",
  "Total Return 6M", "Total Return YTD", "Total Return 1Y", "CAGR 1Y",
  "CAGR 3Y", "CAGR 5Y", "Price Change 52W Low", "Price Change 52W High",
  "All-Time High", "All-Time High Change (%)", "All-Time High Date",
  "All-Time Low", "All-Time Low Change (%)", "All-Time Low Date", "Volume",
  "Open Price", "Low Price", "High Price", "Previous Close", "52 Week Low",
  "52 Week High", "Country", "Employees", "Founded", "Revenue", "Net Income",
  "EPS", "Free Cash Flow", "Assets", "Liabilities", "Gross Margin",
  "Operating Margin", "Profit Margin", "Earnings Yield", "FCF Yield",
  "Debt / Equity", "Interest Coverage Ratio", "Return on Equity",
  "Return on Assets", "Return on Capital", "Current Ratio", "Quick Ratio",
  "Shareholders' Equity", "Tangible Book Value", "Website"
];

const IndicatorDropdown = () => {
  const [selectedIndicators, setSelectedIndicators] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleCheckboxChange = (indicator) => {
    setSelectedIndicators((prev) =>
      prev.includes(indicator)
        ? prev.filter((item) => item !== indicator)
        : [...prev, indicator]
    );
  };

  const resetSelection = () => setSelectedIndicators([]);
  console.log("selectedIndicators", selectedIndicators);

  return (
    <div className="w-full max-w-md mx-auto relative">
      {/* Dropdown Button */}
      <button
        onClick={toggleDropdown}
        className="w-full bg-blue-600 dark:bg-blue-800 text-white px-4 py-2 flex justify-between items-center rounded-lg shadow-md hover:bg-blue-700 dark:hover:bg-blue-900 transition"
      >
        Select Indicators
        <FontAwesomeIcon icon={faChevronDown} />
      </button>

      {/* Dropdown Content */}
      {isOpen && (
        <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg mt-2 shadow-lg max-h-80 overflow-y-auto absolute w-full z-10">
          {/* Search Box */}
          <input
            type="text"
            placeholder="Search indicators..."
            className="w-full p-2 border-b border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 dark:text-white focus:outline-none"
            onChange={(e) => setSearchTerm(e.target.value)}
          />

          {/* Indicators List */}
          <div className="p-2">
            {indicatorsList
              .filter((indicator) =>
                indicator.toLowerCase().includes(searchTerm.toLowerCase())
              )
              .map((indicator, index) => (
                <label
                  key={index}
                  className="flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedIndicators.includes(indicator)}
                    onChange={() => handleCheckboxChange(indicator)}
                  />
                  <span className="dark:text-white">{indicator}</span>
                </label>
              ))}
          </div>

          {/* Reset Button */}
          <button
            onClick={resetSelection}
            className="w-full bg-red-500 dark:bg-red-600 text-white p-2 hover:bg-red-600 dark:hover:bg-red-700 transition sticky bottom-0"
          >
            Reset Selection
          </button>
        </div>
      )}

      {/* Display Selected Indicators */}
      {selectedIndicators.length > 0 && (
        <div className="mt-4 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg shadow dark:text-white">
          <h3 className="font-semibold mb-2">Selected Indicators:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedIndicators.map((indicator, index) => (
              <div
                key={index}
                className="bg-blue-500 dark:bg-blue-700 text-white px-2 py-1 rounded flex items-center space-x-2"
              >
                <span>{indicator}</span>
                <FontAwesomeIcon
                  icon={faTimes}
                  className="w-4 h-4 cursor-pointer"
                  onClick={() => handleCheckboxChange(indicator)}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default IndicatorDropdown;
