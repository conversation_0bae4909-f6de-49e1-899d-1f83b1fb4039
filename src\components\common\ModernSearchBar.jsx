import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import PropTypes from "prop-types";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { fetchStockDetails } from "../../redux/portfolio/portfolioSlice";

/**
 * Modern search bar component with Google Finance inspired styling
 * Supports both light and dark themes
 */
const ModernSearchBar = ({ onSelectStock, portfolioId }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showSearchResults, setShowSearchResults] = useState(false);
  const containerRef = useRef(null);
  const dispatch = useDispatch();

  const navigate = useNavigate();
  const allStocks = useSelector((state) => state.stocks.data || []);
  const stockDetails = useSelector((state) => state.portfolio.stockDetails || {});
  const loadingStockDetails = useSelector((state) => state.portfolio.loadingStockDetails);
  
  // Fetch stock details when component mounts if we have a portfolioId
  useEffect(() => {
    if (portfolioId) {
      dispatch(fetchStockDetails(portfolioId));
    }
  }, [dispatch, portfolioId]);

  // Handle outside clicks to close the dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target)
      ) {
        setShowSearchResults(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [containerRef]);

  // Filter stocks based on search term and enrich with stock details from API
  const filteredStocks = useMemo(() => {
    if (!searchTerm) return [];

    const query = searchTerm.toLowerCase();
    
    return allStocks
      .filter(
        (stock) =>
          stock.symbol.toLowerCase().includes(query) ||
          stock.name.toLowerCase().includes(query)
      )
      .map(stock => {
        // Enrich stock with price and day_gain_percent from stockDetails if available
        const stockDetail = stockDetails[stock.symbol];
        return {
          ...stock,
          price: stockDetail?.price || stock.price,
          day_gain_percent: stockDetail?.day_gain_percent || stock.day_gain_percent,
          exchange: stockDetail?.exchange || stock.exchange || "Tadawul",
          country: stock.country || "Saudi Arabia"
        };
      })
      .slice(0, 5);
  }, [allStocks, searchTerm]);

  // Search handlers
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setShowSearchResults(value.length > 0);
  }, []);

  const handleSearchFocus = useCallback(() => {
    if (searchTerm) {
      setShowSearchResults(true);
    }
  }, [searchTerm]);

  const handleStockClick = useCallback(
    (stock) => {
      if (onSelectStock) {
        // If onSelectStock is provided, use it (for AddInvestmentModal)
        onSelectStock(stock);
      } else {
        // Otherwise navigate to the stock page
        navigate(`/stocks/${stock.symbol}`);
      }
      setShowSearchResults(false);
      setSearchTerm("");
    },
    [navigate, onSelectStock]
  );

  return (
    <div className="mb-8 relative" ref={containerRef}>
      <div className="max-w-2xl mx-auto">
        <div className="relative">
          <Input
            placeholder="Search for stocks, ETFs and more"
            className="w-full py-2 px-4 pr-10 bg-white dark:!bg-gray-700 border border-gray-300 dark:!border-gray-600 rounded-md shadow-sm text-gray-900 dark:!text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={handleSearchFocus}
            size="large"
            suffix={
              <SearchOutlined className="text-gray-500 dark:text-gray-400" />
            }
            style={{
              height: "44px",
              "--antd-wave-shadow-color": "var(--primary-color)",
            }}
          />

          <style>{`
            .ant-input::placeholder {
              color: #9ca3af !important;
            }
            
            .dark .ant-input::placeholder {
              color: #9ca3af !important;
            }
          `}</style>

          {showSearchResults && (
            <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 mt-1">
              <div className="border-b border-gray-200 dark:border-gray-700">
                {/* <div className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300 flex items-center">
                  <span>About these suggestions</span>
                  <span className="ml-2 text-gray-500 dark:text-gray-400 rounded-full border border-gray-400 dark:border-gray-600 w-5 h-5 flex items-center justify-center text-xs">
                    i
                  </span>
                </div> */}
              </div>

              <div className="max-h-80 overflow-y-auto">
                {filteredStocks.length > 0 ? (
                  filteredStocks.map((stock) => (
                    <div
                      key={stock.symbol}
                      className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                      onClick={() => handleStockClick(stock)}
                    >
                      <div className="flex justify-between items-center px-4 py-3">
                        <div>
                          <div className="font-medium text-gray-800 dark:text-white">
                            {stock.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {stock.symbol} : {stock.exchange || "Tadawul"} (
                            {stock.country || "Saudi Arabia"})
                          </div>
                        </div>
                        <div>
                          <div
                            className={`text-right ${
                              // For Saudi market: red for positive, green for negative
                              parseFloat(stock.day_gain_percent || stock.priceChange1D || 0) >= 0
                                ? "text-red-600 dark:text-red-500"
                                : "text-green-600 dark:text-green-500"
                            }`}
                          >
                            {parseFloat(stock.day_gain_percent || stock.priceChange1D || 0) >= 0 ? "▼" : "▲"}{" "}
                            {stock.price ? `${stock.price} SAR` : ""}
                            {stock.day_gain_percent ? ` (${Math.abs(parseFloat(stock.day_gain_percent)).toFixed(2)}%)` : 
                             stock.priceChange1D ? ` (${Math.abs(stock.priceChange1D || 0)?.toFixed(2)}%)` : ""}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-600 dark:text-gray-300">
                    No results found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

ModernSearchBar.propTypes = {
  onSelectStock: PropTypes.func.isRequired,
  portfolioId: PropTypes.string,
};

ModernSearchBar.defaultProps = {
  portfolioId: null,
};

export default ModernSearchBar;
