import { useState } from "react";
import { But<PERSON>, Modal, Checkbox, Tag } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { filterCategories } from "./filtersData";

const FilterPopup = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);

  const handleOpen = () => setIsModalOpen(true);
  const handleClose = () => setIsModalOpen(false);

  const handleFilterChange = (filter) => {
    setSelectedFilters((prev) =>
      prev.includes(filter)
        ? prev.filter((f) => f !== filter)
        : [...prev, filter]
    );
  };

  const handleRemoveFilter = (filter) => {
    setSelectedFilters((prev) => prev.filter((f) => f !== filter));
  };

  const handleClearFilters = () => {
    setSelectedFilters([]);
  };

  return (
    <div className="p-4">
      {/* Filter Section */}
      <div className="border p-4 rounded-lg shadow-lg bg-white dark:bg-gray-800 dark:border-gray-700 dark:text-white">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Filters</h2>
          <Button type="link" danger onClick={handleClearFilters}>
            Remove All Filters
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {selectedFilters.map((filter) => (
            <Tag
              key={filter}
              closable
              onClose={() => handleRemoveFilter(filter)}
              className="px-3 py-1 cursor-pointer bg-white dark:bg-gray-800 dark:text-white"
            >
              {filter}
            </Tag>
          ))}
        </div>
      </div>

      {/* Add Filters Button */}
      <Button type="primary" className="mt-4" onClick={handleOpen}>
        + Add Filters
      </Button>

      {/* Filters Modal */}
      <Modal
        title="Select Filters"
        visible={isModalOpen}
        onCancel={handleClose}
        footer={null}
      >
        <div className="p-4 bg-white dark:bg-gray-800 dark:text-white max-h-80 overflow-y-auto">
          {filterCategories.map((category) => (
            <div key={category.title} className="mb-4">
              <h3 className="font-semibold text-base mb-2">{category.title}</h3>
              <div className="grid grid-cols-2 gap-2">
                {category.filters.map((filter) => (
                  <Checkbox
                    key={filter}
                    checked={selectedFilters.includes(filter)}
                    onChange={() => handleFilterChange(filter)}
                  >
                    {filter}
                  </Checkbox>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default FilterPopup;
