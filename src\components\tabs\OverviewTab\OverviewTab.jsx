// src/components/StockDetails/OverviewTab.jsx

import { useEffect, useState } from "react";
import StockDetailsCard from "./StockDetailsCard";
import Stock<PERSON>hart from "./StockChart";
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
} from "chart.js";
import { useDispatch, useSelector } from "react-redux";
import { getTimeSeriesData } from "../../../redux/stocks/stocksActions";
import { useParams } from "react-router-dom";
import axios from "axios";

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement);

const backendURL = import.meta.env.VITE_BACKEND_URL;

const OverviewTab = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { timeSeriesData, loading, error } = useSelector((state) => state.stocks);

  const [overview, setOverview] = useState(null);
  const [ovLoading, setOvLoading] = useState(false);
  const [ovError, setOvError] = useState(null);

  // Fetch historical chart data
  useEffect(() => {
    dispatch(getTimeSeriesData({ symbol: id, interval: "1day", outputsize: 30 }));
  }, [dispatch, id]);

  // Fetch summary metrics
  useEffect(() => {
    const fetchOverview = async () => {
      setOvLoading(true);
      setOvError(null);
      try {
        const res = await axios.get(
          `${backendURL}/details/stocks-overview`,
          { params: { symbol: id } }
        );
        setOverview(res.data.response.data);
      } catch (err) {
        setOvError(err.response?.data?.response?.message || err.message);
      } finally {
        setOvLoading(false);
      }
    };

    if (id) fetchOverview();
  }, [id]);

  if (loading || ovLoading) return <p>Loading overview…</p>;
  if (error) return <p>Error loading chart: {error}</p>;
  if (ovError) return <p>Error loading metrics: {ovError}</p>;

  const metrics = overview
    ? [
        { id: "marketCap", label: "Market Cap", value: overview.Market_Cap ?? "N/A" },
        { id: "revenue", label: "Revenue (TTM)", value: overview.revenue ?? "N/A" },
        { id: "netIncome", label: "Net Income (TTM)", value: overview.net_income ?? "N/A" },
        { id: "sharesOut", label: "Shares Out", value: overview.Shares_Out ?? "N/A" },
        { id: "eps", label: "EPS (TTM)", value: overview.eps ?? "N/A" },
        { id: "priceOpen", label: "Price Open", value: overview.Price_Open ?? "N/A" },
        { id: "priceClose", label: "Price Close", value: overview.Price_Close ?? "N/A" },
        { id: "volume", label: "Volume", value: overview.Volume ?? "N/A" },
        { id: "peRatio", label: "PE Ratio (TTM)", value: overview.peRatio ?? "N/A" },
        { id: "divYield", label: "Dividend Yield", value: overview.Dividend_Yield ?? "N/A" },
      ]
    : [];

  const labels = timeSeriesData?.values?.map((e) => e.datetime) || [];
  const prices = timeSeriesData?.values?.map((e) => parseFloat(e.close)) || [];

  const chartData = {
    data: {
      labels,
      datasets: [
        {
          label: "Stock Price",
          data: prices,
          borderColor: "red",
          fill: false,
          tension: 0.1,
        },
      ],
    },
    options: {
      responsive: true,
      scales: {
        x: { title: { display: true, text: "Date" } },
        y: { title: { display: true, text: "Price (USD)" } },
      },
    },
    timeRanges: ["1 Day", "5 Days", "1 Month", "3 Months", "YTD", "1 Year", "5 Years", "Max"],
  };

  return (
    <div className="flex flex-col justify-around items-center sm:flex-row gap-2 p-4">
      <StockDetailsCard metrics={metrics} />
      <StockChart chartData={chartData} />
    </div>
  );
};

export default OverviewTab;
