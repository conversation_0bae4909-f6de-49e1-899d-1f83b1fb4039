import React, { useEffect, useState } from 'react';
import { Modal, Radio, Button } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { loadWatchlists } from '../../redux/watchlist/watchlistSlice';

export default function WatchlistSelectModal({ visible, onClose, onConfirm }) {
  const dispatch = useDispatch();
  const { items, loading } = useSelector(state => state.watchlist);
  const [selectedId, setSelectedId] = useState(null);

  useEffect(() => {
    if (visible) {
      dispatch(loadWatchlists());
      setSelectedId(null);
    }
  }, [visible, dispatch]);

  const handleOk = () => {
    if (selectedId) {
      onConfirm(selectedId);
      onClose();
    }
  };

  return (
    <Modal
      title="Select Watchlist"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="back" onClick={onClose}>Cancel</Button>,
        <Button key="submit" type="primary" disabled={!selectedId} onClick={handleOk}>Add</Button>
      ]}
    >
      {loading ? (
        <p>Loading watchlists...</p>
      ) : (
        <Radio.Group
          onChange={e => setSelectedId(e.target.value)}
          value={selectedId}
          style={{ display: 'flex', flexDirection: 'column' }}
        >
          {items.map(wl => (
            <Radio key={wl.id} value={wl.id} style={{ margin: '8px 0' }}>
              {wl.name}
            </Radio>
          ))}
        </Radio.Group>
      )}
    </Modal>
  );
}