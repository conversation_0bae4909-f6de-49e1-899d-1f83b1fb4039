import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

// Define RTL languages
const RTL_LANGUAGES = ["ar", "he", "fa", "ur"];

// Configure language direction
const configureLanguageDirection = (lng) => {
  const dir = RTL_LANGUAGES.includes(lng) ? "rtl" : "ltr";
  document.documentElement.dir = dir;
  document.documentElement.lang = lng;
};

// Initialize i18n
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    fallbackLng: "en",
    // debug: import.meta.env.MODE === "development",
    debug: true, // ====>>>>>TRUE IF THE PROJECT IS IN DEVELOPMENT STAGE
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ["localStorage", "navigator"],
      caches: ["localStorage"],
    },
    react: {
      useSuspense: true,
    },
  });

// Set initial language direction
configureLanguageDirection(i18n.language);

// Change direction when language changes
i18n.on("languageChanged", (lng) => {
  configureLanguageDirection(lng);
});

export default i18n;
