// src/components/AdminProtectedRoute.jsx
import  { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { fetchUserProfile } from "../redux/user/userActions";

const ProtectedAdminRoute = ({ requiredRole = "admin" }) => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.user.userInfo);
  const loading = useSelector((state) => state.user.loading);
  const [isFetched, setIsFetched] = useState(false);

  useEffect(() => {
    if (!user && !isFetched) {
      dispatch(fetchUserProfile()).then(() => setIsFetched(true));
    } else {
      setIsFetched(true);
    }
  }, [dispatch, user, isFetched]);

  if (loading || !isFetched) return <div>Loading...</div>;

  if (!user || !user.role?.includes(requiredRole)) {
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};
ProtectedAdminRoute.propTypes = {
  requiredRole: PropTypes.string,
};

export default ProtectedAdminRoute;
