import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedFilters: [],
};

const filterSlice = createSlice({
  name: "filters",
  initialState,
  reducers: {
    addFilter: (state, action) => {
        console.log("Adding filter to Redux:", action.payload);
      if (!state.selectedFilters.includes(action.payload)) {
        state.selectedFilters.push(action.payload);
      }
    },
    removeFilter: (state, action) => {
      state.selectedFilters = state.selectedFilters.filter((f) => f !== action.payload);
    },
    clearFilters: (state) => {
      state.selectedFilters = [];
    },
  },
});

export const { addFilter, removeFilter, clearFilters } = filterSlice.actions;
export default filterSlice.reducer;
