import axios from "axios";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "react-toastify";
import { logout, setCredentials } from "./userSlice";
//import * as jwt_decode from 'jwt-decode';  // Use this if the default import doesn't work
import api from "../../utils/api";

const backendURL = import.meta.env.VITE_BACKEND_URL;

export const registerUser = createAsyncThunk(
  "auth/register",
  async ({ firstName, email, password }, { rejectWithValue }) => {
    try {
      const config = { headers: { "Content-Type": "application/json" } };
      const response = await axios.post(
        `${backendURL}/auth/register`,
        { userName: firstName, email, password },
        config
      );
      if (response.status !== 201) {
        const errorMsg = "Error registering. Please try again.";
        toast.error(errorMsg);
        return rejectWithValue(errorMsg);
      }
      return response.data;
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) || error.message;
      return rejectWithValue(errorMsg);
    }
  }
);

export const userLogin = createAsyncThunk(
  "auth/login",
  async ({ email, password }, { rejectWithValue, dispatch }) => {
    try {
      const response = await api.post("/auth/login", { email, password });
      console.log("Login Response Data:", response.data);

      if (!response.data.user || !response.data.token) {
        throw new Error("Invalid login response. Token or user missing.");
      }

      dispatch(
        setCredentials({ user: response.data.user, token: response.data.token })
      );
      toast.success("Login successful!");

      return response.data.user;
    } catch (error) {
      console.error("Login Error:", error);

      toast.error(
        "Login failed: " + (error.response?.data?.message || error.message)
      );

      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

export const fetchUserProfile = createAsyncThunk(
  "auth/fetchUserProfile",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get("/protected/me");
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

export const refreshToken = createAsyncThunk(
  "auth/refreshToken",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post("/auth/refresh");
      return response.data;
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) || error.message;
      return rejectWithValue(errorMsg);
    }
  }
);

export const forgotPassword = createAsyncThunk(
  "auth/forgotPassword",
  async ({ email }, { rejectWithValue }) => {
    try {
      const config = { headers: { "Content-Type": "application/json" } };
      const response = await axios.post(
        `${backendURL}/auth/forgot-password`,
        { email },
        config
      );
      if (response.status === 200) {
        return response.data;
      } else {
        const errorMsg = "Unexpected response. Please try again.";
        return rejectWithValue(errorMsg);
      }
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) || error.message;
      return rejectWithValue(errorMsg);
    }
  }
);

export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async (
    { token, email, newPassword, confirmPassword },
    { rejectWithValue }
  ) => {
    try {
      const config = { headers: { "Content-Type": "application/json" } };
      const response = await axios.post(
        `${backendURL}/auth/reset-password`,
        { token, email, newPassword, confirmPassword },
        config
      );
      // Expecting status 201 for a successful password reset
      if (response.status === 201) {
        return response.data;
      } else {
        const errorMsg = "Unexpected response. Please try again.";
        return rejectWithValue(errorMsg);
      }
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) || error.message;
      return rejectWithValue(errorMsg);
    }
  }
);

export const userLogout = createAsyncThunk(
  "auth/logout",
  async (_, { rejectWithValue, dispatch }) => {
    try {
      await axios.post(
        `${backendURL}/auth/logout`,
        {},
        { withCredentials: true }
      );
      dispatch(logout());
      return true;
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) || error.message;
      return rejectWithValue(errorMsg);
    }
  }
);

export const verifyEmail = createAsyncThunk(
  "auth/verifyEmail",
  async (token, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${backendURL}/auth/verify-email/${token}`,
        { withCredentials: true }
      );
      if (response.status === 200) {
        return response.data;
      } else {
        return rejectWithValue("Verification failed. Please try again.");
      }
    } catch (error) {
      const errorMsg =
        (error.response && error.response.data.message) ||
        "Verification failed.";
      return rejectWithValue(errorMsg);
    }
  }
);
