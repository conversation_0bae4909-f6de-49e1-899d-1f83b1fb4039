import { createSlice } from "@reduxjs/toolkit";
import { userLogin, fetchUserProfile, userLogout } from "./userActions";

const initialState = {
  userInfo: null,
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    logout: (state) => {
      state.userInfo = null;
      localStorage.removeItem("token"); // Cleanup stored token (if any)
    },
    setCredentials: (state, { payload }) => {
      // console.log("Updating Redux User Info:", payload);

      // Store the token in localStorage if it exists
      if (payload && payload.token) {
        // console.log("Storing token in localStorage");
        localStorage.setItem("token", payload.token);
      }

      state.userInfo = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(userLogin.pending, (state) => {
        state.loading = true;
      })
      .addCase(userLogin.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(userLogin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.userInfo = action.payload;
      })
      .addCase(userLogout.fulfilled, (state) => {
        state.userInfo = null;
      });
  },
});

export const { logout, setCredentials } = userSlice.actions;
export default userSlice.reducer;
