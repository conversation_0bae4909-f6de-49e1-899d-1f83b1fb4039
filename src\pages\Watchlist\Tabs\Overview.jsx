// src/components/Watchlist/Tabs/Overview.jsx
import React from "react";
import { Table, Checkbox } from "antd";

export default function Overview({
  data = [],
  selectedRowKeys = [],
  onChange = () => {}
}) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    { title: "Price", dataIndex: "price" },
    {
      title: "1D Change",
      dataIndex: "dayChange",
      render: (txt) => {
        const value = txt ?? "-";
        return (
          <span
            className={
              typeof txt === "string" && txt.startsWith("-")
                ? "text-red-500"
                : "text-green-500"
            }
          >
            {value}
          </span>
        );
      },
    },
    {
      title: "Div. Yield",
      dataIndex: "divYield",
      render: (txt) => {
        const value = txt ?? "-";
        return (
          <span
            className={
              typeof txt === "string" && txt.startsWith("-")
                ? "text-red-500"
                : "text-green-500"
            }
          >
            {value}
          </span>
        );
      },
    },
    { title: "P/E Ratio", dataIndex: "peRatio" },
    { title: "52W High", dataIndex: "high52" },
    { title: "52W Low", dataIndex: "low52" },
    { title: "Market Cap", dataIndex: "marketCap" },
  ];

  return (
    <Table
      columns={columns}
      dataSource={Array.isArray(data) ? data : []}
      pagination={false}
      rowKey="key"
    />
  );
}
