{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.5.1", "@tailwindcss/vite": "^4.0.5", "antd": "^5.24.1", "axios": "^1.7.9", "chart.js": "^4.4.8", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-chart-financial": "^0.2.1", "chartjs-plugin-zoom": "^2.2.0", "framer-motion": "^12.6.3", "frontend": "file:", "fuse.js": "^7.1.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^3.0.2", "jwt-decode": "^4.0.0", "lightweight-charts": "^5.0.6", "lodash.debounce": "^4.0.8", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^14.1.3", "react-redux": "^9.2.0", "react-router": "^7.1.5", "react-router-dom": "^7.1.5", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "tailwindcss": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}