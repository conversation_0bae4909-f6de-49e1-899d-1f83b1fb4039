import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export const chartApi = createApi({
  reducerPath: 'chartApi',
  baseQuery: fetchBaseQuery({ baseUrl: 'https://api.twelvedata.com' }),
  endpoints: (builder) => ({
    getTimeSeries: builder.query({
      query: ({ symbol, interval, outputsize }) => ({
        url: '/time_series',
        params: {
          symbol,
          interval,
          outputsize,
          apikey: import.meta.env.VITE_TWELVEDATA_API_KEY
        }
      })
    })
  })
})

export const { 
  useGetTimeSeriesQuery,
  useLazyGetTimeSeriesQuery 
} = chartApi