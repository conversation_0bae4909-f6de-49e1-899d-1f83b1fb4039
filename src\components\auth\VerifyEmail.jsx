import { useEffect, useState, useRef } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import { useDispatch } from "react-redux";
import { verifyEmail } from "../../redux/user/userActions";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const VerifyEmail = () => {
  const { token } = useParams();
  const dispatch = useDispatch();
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [message, setMessage] = useState("");
  const didVerify = useRef(false);

  useEffect(() => {
    if (token && !didVerify.current) {
      didVerify.current = true;
      dispatch(verifyEmail(token))
        .unwrap()
        .then((data) => {
          setVerificationStatus("success");
          setMessage(data.message);
          toast.success("Email verified successfully! Please login.");
        })
        .catch((error) => {
          setVerificationStatus("error");
          setMessage(error);
          toast.error(error);
        });
    }
  }, [token, dispatch]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900">
      <ToastContainer 
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        className="bg-white shadow-lg rounded-lg p-4"
        bodyClassName="text-gray-800 dark:text-white"
        progressClassName="bg-green-500"
      />
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        {verificationStatus === "success" ? (
          <>
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Email Verified</h2>
            <Link to="/login" className="text-blue-500 dark:text-blue-300 hover:underline">
              Go to Login
            </Link>
          </>
        ) : verificationStatus === "error" ? (
          <>
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Verification Failed</h2>
            <p className="mb-4 text-gray-900 dark:text-white">{message}</p>
            <Link to="/signup" className="text-blue-500 dark:text-blue-300 hover:underline">
              Try Signing Up Again
            </Link>
          </>
        ) : (
          <p className="text-gray-900 dark:text-white">Verifying...</p>
        )}
      </div>
    </div>
  );
};

export default VerifyEmail;
