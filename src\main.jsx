import { StrictMode, useEffect } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { RouterProvider } from "react-router-dom";
import router from "./routes/AppRoutes.jsx";
import { Provider, useSelector } from "react-redux";
import { store } from "./store";
import ErrorBoundary from "./components/ErrorBoundary";
// Import i18n configuration
import "./i18n/config";

const DarkModeHandler = ({ children }) => {
  const darkModeEnabled = useSelector((state) => state.darkMode.enabled);

  useEffect(() => {
    if (darkModeEnabled) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkModeEnabled]);

  return children;
};

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <DarkModeHandler>
          <RouterProvider router={router} />
        </DarkModeHandler>
      </Provider>
    </ErrorBoundary>
  </StrictMode>
);
