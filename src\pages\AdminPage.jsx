// src/pages/AdminPage.jsx
import React, { useState } from "react";
import axios from "axios";

const backendURL = import.meta.env.VITE_BACKEND_URL;

const AdminPage = () => {
  // match exactly the backend route names
  const [indexType, setIndexType] = useState("tasi");    // 'tasi' | 'numo'
  const [file, setFile] = useState(null);
  const [statusMsg, setStatusMsg] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  const handleIndexChange = (e) => {
    setIndexType(e.target.value);
    setStatusMsg("");
  };

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setStatusMsg("");
  };

  const handleUpload = async () => {
    if (!file) {
      setStatusMsg("❌ Please select an Excel file first.");
      return;
    }

    // Guard: only upload to matching endpoint
    // (you could expand this to inspect file contents or name if needed)
    if (
      (indexType === "tasi" && /numo/i.test(file.name)) ||
      (indexType === "numo" && /tasi/i.test(file.name))
    ) {
      setStatusMsg("❌ Wrong index selected for this file.");
      return;
    }

    setIsUploading(true);
    setStatusMsg("Uploading…");

    const formData = new FormData();
    formData.append("file", file);

    try {
      // POSTS to /files/upload/tasi or /files/upload/numo
      const res = await axios.post(
        `${backendURL}/files/upload/${indexType}`,
        formData,
        { headers: { "Content-Type": "multipart/form-data" } }
      );
      setStatusMsg(
        `✅ ${res.data.response?.message || "Upload successful!"}`
      );
    } catch (err) {
      console.error("Upload error:", err);
      const msg =
        err.response?.data?.response?.message ||
        err.response?.data?.message ||
        err.message;
      setStatusMsg(`❌ ${msg}`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-lg mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Admin Upload Dashboard</h1>

      {/* Dropdown to choose TASI or NOMU */}
      <div>
        <label className="block mb-1 font-medium">Select Index:</label>
        <select
          value={indexType}
          onChange={handleIndexChange}
          className="w-full p-2 border rounded"
        >
          <option value="tasi">TASI</option>
          <option value="numo">NOMU</option>
        </select>
      </div>

      {/* File input */}
      <div>
        <label className="block mb-1 font-medium">Choose Excel File:</label>
        <input
          type="file"
          accept=".xls,.xlsx"
          onChange={handleFileChange}
          className="w-full"
        />
      </div>

      {/* Upload button */}
      <button
        onClick={handleUpload}
        disabled={isUploading}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded disabled:opacity-50"
      >
        {isUploading ? "Uploading…" : "Upload File"}
      </button>

      {/* Status message */}
      {statusMsg && (
        <div className="mt-4 text-center text-gray-700">{statusMsg}</div>
      )}
    </div>
  );
};

export default AdminPage;
