import React from "react";
import { Table, Checkbox } from "antd";

export default function Earnings({ data, selectedRowKeys, onChange }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "company" },
    {
      title: "EPS TTM",
      dataIndex: "eps",
      render: (txt) => String(txt ?? "N/A"),
    },
    {
      title: "Rev. Growth TTM",
      dataIndex: "revGrowth",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
    {
      title: "Profit Growth TTM",
      dataIndex: "profitGrowth",
      render: (txt) => {
        const str = String(txt ?? "0%");
        return (
          <span className={(str || "").startsWith("-") ? "text-red-500" : "text-green-500"}>
            {str}
          </span>
        );
      },
    },
  ];

  return <Table columns={columns} dataSource={data} pagination={false} />;
}
