import { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  loadWatchlists,
  createNewWatchlist,
  renameExistingWatchlist,
  deleteExistingWatchlist
} from '../../redux/watchlist/watchlistSlice';
import { Pencil, Trash, Check, X } from 'lucide-react';
import WatchlistModal from './WatchlistModal';

export default function WatchlistDropdown({ onSelect }) {
  const dispatch = useDispatch();
  const { items, loading } = useSelector(state => state.watchlist);
  const [open, setOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [editingIndex, setEditingIndex] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [showModal, setShowModal] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => { dispatch(loadWatchlists()); }, [dispatch]);
  useEffect(() => { if (items.length) onSelect(items[activeIndex]); }, [items, activeIndex]);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setOpen(false);
        setEditingIndex(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleEdit = (idx) => {
    setEditingIndex(idx);
    setEditValue(items[idx].name);
  };

  const saveEdit = (idx) => {
    if (editValue.trim()) {
      dispatch(renameExistingWatchlist({ id: items[idx].id, newName: editValue }));
      setEditingIndex(null);
    }
  };

  const handleDelete = (idx) => dispatch(deleteExistingWatchlist(items[idx].id));

  const handleSaveNew = (name) => {
    dispatch(createNewWatchlist(name));
    setShowModal(false);
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <button onClick={() => setOpen(!open)} className="border px-4 py-2 rounded-md">
        {items[activeIndex]?.name || 'No Watchlist'} ▼
      </button>

      {open && (
        <div className="absolute z-10 mt-2 w-64 bg-white border rounded shadow max-h-80 overflow-y-auto">
          {items.map((w, i) => (
            <div key={w.id} className={`px-3 py-2 flex items-center justify-between ${i === activeIndex ? 'bg-gray-100' : ''}`}>
              {editingIndex === i ? (
                <div className="flex items-center gap-1 w-full">
                  <input
                    value={editValue}
                    onChange={e => setEditValue(e.target.value)}
                    className="border p-1 rounded w-full text-sm"
                  />
                  <Check onClick={() => saveEdit(i)} className="text-green-600 hover:text-green-800 cursor-pointer" size={18} />
                  <X onClick={() => setEditingIndex(null)} className="text-red-500 hover:text-red-700 cursor-pointer" size={18} />
                </div>
              ) : (
                <>
                  <span className="flex-1 cursor-pointer text-sm truncate" onClick={() => { setActiveIndex(i); onSelect(w); }}>{w.name}</span>
                  <div className="flex items-center gap-2">
                    <Pencil onClick={() => handleEdit(i)} className="text-blue-500 hover:text-blue-700 cursor-pointer" size={16} />
                    <Trash onClick={() => handleDelete(i)} className="text-red-500 hover:text-red-700 cursor-pointer" size={16} />
                  </div>
                </>
              )}
            </div>
          ))}
          <div onClick={() => setShowModal(true)} className="px-3 py-2 border-t text-blue-600 hover:bg-gray-50 cursor-pointer text-sm">+ Add Watchlist</div>
        </div>
      )}

      <WatchlistModal isOpen={showModal} onClose={() => setShowModal(false)} onSave={handleSaveNew} />
    </div>
  );
}
