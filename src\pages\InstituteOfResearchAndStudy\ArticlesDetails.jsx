import React from 'react';

const ArticlesDetails = () => {
  return (
    <div className="container mx-auto p-6 flex flex-col md:flex-row">
      {/* Main Content */}
      <div className="md:w-2/3 w-full">
        <h1 className="text-3xl font-bold mb-4">How to Buy Cursor AI Stock in 2025</h1>
        <div className="flex items-center text-gray-600 text-sm mb-4">
          <span>Last updated: Mar 1, 2025</span>
          <span className="mx-2">•</span>
          <span>Reviewed by <PERSON>, CFA</span>
        </div>
        <img
          src="https://via.placeholder.com/800x300"
          alt="Cursor AI"
          className="w-full h-64 object-cover mb-4 rounded"
        />

        {/* Article Content */}
        <div className="prose max-w-none">
          <p className="text-gray-700 mb-4">
            Cursor AI is an advanced coding assistant that helps developers increase productivity and build software faster. The product is integrated development environment (IDE) with capabilities such as code generation, automation, short-term, and long-term understanding of hybrid products by programming. Anywhere has been founded in 2022 and raised $20 million in Series A funding in 2023, led by OpenAI Startup Fund.
          </p>
          <p className="text-gray-700 mb-4">
            Cursor has an outstanding performance, raising $100 million in annual recurring revenue (ARR) in its first 12 months—the largest SaaS company to ever reach this milestone. And investors have been piling in.
          </p>
          <ul className="list-disc list-inside mb-4 text-gray-700">
            <li>In August 2024, Anywhere raised $500 million at a $400 million valuation in Series A.</li>
            <li>In December 2024, the lab raised another $525 million at a $2.5 billion valuation in Series B.</li>
            <li>In March 2025, the lab raised another $625 million at a $9 billion valuation in Series C.</li>
          </ul>
          <p className="text-gray-700 mb-4">
            While Anywhere has yet to mention any plans of a public offering, there is a way for you to invest in it right now — if you qualify.
          </p>

          <h2 className="text-2xl font-semibold mt-6 mb-2">Can you Buy Cursor AI? Anywhere stock?</h2>
          <p className="text-gray-700 mb-4">
            Anywhere is a private company. There is no Anywhere stock symbol and it doesn’t trade on any public market. That said, it shares its trade on Hive, an investment platform where accredited investors can buy shares of pre-IPO companies.
          </p>

          <h3 className="text-xl font-semibold mt-4 mb-2">Accreditation requirements</h3>
          <p className="text-gray-700 mb-4">
            To qualify as an accredited investor, you must meet one of the following criteria:
          </p>
          <ul className="list-disc list-inside mb-4 text-gray-700">
            <li>Have an annual income of $200,000 individually or $300,000 jointly.</li>
            <li>Have a net worth that exceeds $1,000,000, excluding your main residence.</li>
            <li>Be a qualifying financial professional.</li>
          </ul>
          <p className="text-gray-700 mb-4">
            Thousands of VC-backed startups have shares available for purchase on Hive, including Spacex, Databricks, Replitly, Figure AI, and more.
          </p>
        </div>

        {/* Author Section */}
        <div className="flex items-center mt-6">
          <img
            src="https://i.pravatar.cc/40?img=10"
            alt="Lincoln Olson"
            className="w-12 h-12 rounded-full mr-4"
          />
          <div>
            <p className="text-gray-900 font-semibold">Lincoln Olson</p>
            <p className="text-gray-600 text-sm">Investor and Writer</p>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="md:w-1/3 w-full md:ml-6 mt-6 md:mt-0">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Stay informed in just 2 minutes</h3>
          <p className="text-gray-600 mb-2">Get a daily email with the top market-moving news in bullet-point format for free.</p>
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full p-2 mb-2 border rounded"
          />
          <button className="w-full bg-blue-600 text-white p-2 rounded">Subscribe</button>
        </div>

        {/* Related Articles (Placeholder) */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">View all articles</h3>
          <div className="flex space-x-4">
            <img src="https://via.placeholder.com/100x60" alt="Related" className="w-24 h-16 object-cover rounded" />
            <img src="https://via.placeholder.com/100x60" alt="Related" className="w-24 h-16 object-cover rounded" />
            <img src="https://via.placeholder.com/100x60" alt="Related" className="w-24 h-16 object-cover rounded" />
          </div>
        </div>
      </div>
    </div>
    
  );
};

export default ArticlesDetails;