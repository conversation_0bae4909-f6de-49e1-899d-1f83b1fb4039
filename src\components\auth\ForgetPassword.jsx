import { useState } from "react";
import { useDispatch } from "react-redux";
import { forgotPassword } from "../../redux/user/userActions";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";

const AccountRecovery = () => {
  const [formData, setFormData] = useState({ email: "" });
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await dispatch(forgotPassword({ email: formData.email })).unwrap();
      toast.success(t("auth.resetLinkSent"));
    } catch (error) {
      toast.error(error);
    }
  };

  return (
    <section className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={isRTL}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        className="bg-white shadow-lg rounded-lg p-4"
        bodyClassName="text-gray-800 dark:text-white"
        progressClassName="bg-green-500"
      />
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:min-h-screen lg:py-0">
        <div className="flex items-center justify-between w-full max-w-md mb-6">
          <a
            href="#"
            className="flex items-center text-2xl font-semibold text-gray-900 dark:text-white"
          >
            <div className="text-2xl font-bold flex gap-2 items-center">
              <div className="w-10 h-10 bg-black/10 rounded-full" />
              {t("app.title")}
            </div>
          </a>
          <LanguageSwitcher />
        </div>
        <div className="w-full p-6 bg-white dark:bg-gray-800 rounded-lg shadow dark:border sm:max-w-md">
          <h1 className="mb-1 text-xl font-bold text-gray-900 dark:text-white">
            {t("auth.forgotPasswordTitle")}
          </h1>
          <p className="font-light text-gray-500 dark:text-gray-400">
            {t("auth.forgotPasswordText")}
          </p>
          <form className="mt-4 space-y-4" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="email"
                className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
              >
                {t("auth.yourEmail")}
              </label>
              <input
                type="email"
                name="email"
                id="email"
                value={formData.email}
                onChange={handleChange}
                className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-lg block w-full p-2.5"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 font-medium rounded-lg text-sm px-5 py-2.5 shadow-lg transition-all duration-300 cursor-pointer"
            >
              {t("auth.resetPassword")}
            </button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default AccountRecovery;
