import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import {
//   faFacebook,
//   faInstagram,
//   faTwitter,
//   faLinkedin,
//   faYoutube,
// } from '@fortawesome/free-brands-svg-icons';

const Footer = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  // Text alignment based on language direction
  const textAlignClass = isRTL ? "text-right" : "text-left";

  return (
    <footer className="p-4 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white">
      <div className="container mx-auto">
        {/* Quick Links and Contact */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Contact Us */}
          <div className={textAlignClass}>
            <h3 className="text-xl font-bold mb-4">{t("footer.contactUs")}</h3>
            <ul className="space-y-2">
              <li>
                <div className="font-semibold">{t("footer.phone")}:</div>
                <div className="text-gray-700 dark:text-gray-300" dir="ltr">
                  {t("footer.phoneNumber")}
                </div>
              </li>
              <li>
                <div className="font-semibold">{t("footer.email")}:</div>
                <div className="text-gray-700 dark:text-gray-300">
                  {t("footer.emailAddress")}
                </div>
              </li>
              <li>
                <div className="font-semibold">{t("footer.address")}:</div>
                <div className="text-gray-700 dark:text-gray-300">
                  {t("footer.fullAddress")}
                </div>
              </li>
            </ul>
          </div>

          {/* Quick Links */}
          <div className={textAlignClass}>
            <h3 className="text-xl font-bold mb-4">{t("footer.quickLinks")}</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  to="/"
                  className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {t("footer.home")}
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {t("footer.about")}
                </Link>
              </li>
              <li>
                <Link
                  to="/pricing"
                  className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {t("footer.pricing")}
                </Link>
              </li>
              <li>
                <Link
                  to="/signup"
                  className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {t("footer.signUp")}
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {t("footer.contactUs")}
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
