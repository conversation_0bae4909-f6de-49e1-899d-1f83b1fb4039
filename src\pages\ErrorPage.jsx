// src/pages/ErrorPage.jsx
import { useRouteError } from "react-router-dom";

export default function ErrorPage() {
  const error = useRouteError();
  console.error("Routing error:", error);

  return (
    <div className="p-8 text-center bg-white dark:bg-gray-900 dark:text-white">
      <h1 className="text-3xl font-bold mb-4">Oops! Something went wrong.</h1>
      <p className="text-lg">
        <i>{error.statusText || error.message || "An unexpected error occurred."}</i>
      </p>
    </div>
  );
}
