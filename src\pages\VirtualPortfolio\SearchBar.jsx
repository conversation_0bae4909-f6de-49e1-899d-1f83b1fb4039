import { useState, useMemo, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { fetchStocks } from "../../redux/stocks/stocksActions";
import { Input, Dropdown } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import debounce from "lodash.debounce";

const SearchBar = ({ onSelectStock, instanceId }) => {
  const [query, setQuery] = useState("");
  const [filteredStocks, setFilteredStocks] = useState([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dispatch = useDispatch();
  const { data: allStocks, status } = useSelector((state) => state.stocks);
  console.log("allStocks", allStocks);
  // Fetch stocks on mount
  useEffect(() => {
    if (status === "idle") {
      dispatch(fetchStocks());
    }
  }, [dispatch, status]);

  // Debounced search handler
  const debouncedSearch = useMemo(
    () =>
      debounce((value) => {
        if (!value) {
          setFilteredStocks([]);
          setDropdownOpen(false);
          return;
        }
        const q = value.toLowerCase();
        const results = allStocks
          .filter(
            (s) =>
              s.symbol.toLowerCase().includes(q) ||
              s.name.toLowerCase().includes(q)
          )
          .slice(0, 10);
        setFilteredStocks(results);
        setDropdownOpen(results.length > 0);
      }, 300),
    [allStocks]
  );

  // Clean up debounce on unmount
  useEffect(() => () => debouncedSearch.cancel(), [debouncedSearch]);
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    debouncedSearch(value);
  };

  const handleSelectStock = (stock) => {
    onSelectStock(stock);
    setQuery(""); // Clear input after selection
    setFilteredStocks([]); // Clear suggestions
    setDropdownOpen(false);
  };

  const dropdownContent = (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {filteredStocks.map((stock) => (
        <div
          key={stock.symbol}
          onClick={() => handleSelectStock(stock)}
          className="p-3 hover:bg-gray-100 cursor-pointer flex justify-between border-b border-gray-100"
        >
          <div>
            <div className="flex items-center">
              <div className="bg-gray-200 rounded p-1 mr-2 w-12 text-center text-xs">
                {stock.symbol}
              </div>
              <span>{stock.name}</span>
            </div>
          </div>
          <div className="text-right">
            {stock.price && <div>{stock.price} SAR</div>}
            {(stock.day_gain_percent !== undefined || stock.priceChange1D !== undefined) && (
              <div
                className={`text-xs ${
                  // For Saudi market: red for positive, green for negative
                  parseFloat(stock.day_gain_percent || stock.priceChange1D || 0) >= 0
                    ? "text-red-500"
                    : "text-green-500"
                }`}
              >
                {parseFloat(stock.day_gain_percent || stock.priceChange1D || 0) >= 0 ? "▼" : "▲"}{" "}
                {stock.day_gain_percent
                  ? `${Math.abs(parseFloat(stock.day_gain_percent)).toFixed(2)}%`
                  : stock.priceChange1D
                    ? `${Math.abs(stock.priceChange1D || 0)?.toFixed(2)}%`
                    : ""}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="relative w-full max-w-3xl mx-auto">
      <Dropdown
        open={dropdownOpen}
        dropdownRender={() => dropdownContent}
        placement="bottomCenter"
        arrow={false}
        trigger={["click"]}
        onOpenChange={(open) =>
          setDropdownOpen(open && filteredStocks.length > 0)
        }
      >
        <Input
          size="middle"
          value={query}
          onChange={handleInputChange}
          placeholder="Search for stocks by name or symbol..."
          prefix={<SearchOutlined />}
          className="dark:bg-gray-700 rounded-full shadow-md"
          onClick={() => setDropdownOpen(filteredStocks.length > 0)}
        />
      </Dropdown>
    </div>
  );
};

export default SearchBar;
