import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";

const ValuationTabs = ({ activeTab, onTabChange }) => {
  const { t } = useTranslation();

  const tabs = [
    { id: "pe", label: t("valuation.tabs.peModel") || "P/E Model" },
    {
      id: "revenue",
      label: t("valuation.tabs.revenueGrowth") || "Revenue Growth Method",
    },
    {
      id: "netProfit",
      label: t("valuation.tabs.netProfitGrowth") || "Net Profit Growth Method",
    },
    {
      id: "saved",
      label: t("valuation.tabs.savedValuation") || "Saved Valuation",
    },
  ];

  return (
    <div className="grid grid-cols-4 border border-gray-200 dark:border-gray-700 rounded-t-lg overflow-hidden">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={`px-6 py-3 font-medium text-sm focus:outline-none transition-colors
            ${
              activeTab === tab.id
                ? "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-b-2 border-indigo-600 dark:border-indigo-500"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 border-b border-gray-200 dark:border-gray-600"
            }`}
          onClick={() => onTabChange(tab.id)}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

ValuationTabs.propTypes = {
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired,
};

export default ValuationTabs;
