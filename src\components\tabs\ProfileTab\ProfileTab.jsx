// src/components/StockDetails/ProfileTab.jsx

import { useState, useEffect } from "react";
import { Card, Spin, Alert, Descriptions } from "antd";
import axios from "axios";
import { useParams } from "react-router-dom";

const backendURL = import.meta.env.VITE_BACKEND_URL;

const ProfileTab = () => {
  const { id: symbol } = useParams();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchProfile = async () => {
    setLoading(true);
    setError(null);

    try {
      const res = await axios.get(`${backendURL}/details/stocks-profile`, {
        params: { symbol },
      });

      const { data } = res.data.response;
      if (!Array.isArray(data) || data.length === 0) {
        throw new Error("No profile data returned");
      }
      setProfile(data[0]);
    } catch (err) {
      setError(
        err.response?.data?.response?.message ||
        err.message ||
        "Unknown error"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (symbol) fetchProfile();
  }, [symbol]);

  if (loading) {
    return (
      <div className="text-center py-10">
        <Spin tip="Loading profile…" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        type="error"
        message="Error fetching profile"
        description={error}
        showIcon
      />
    );
  }

  return (
    <Card
      title={`Company Profile: ${symbol}`}
      className="max-w-5xl mx-auto px-2 sm:px-4 md:px-6"
    >
      {profile && (
        <Descriptions
          bordered
          column={{ xs: 1, sm: 1, md: 2, lg: 3 }}
          size="middle"
          layout="horizontal"
        >
          <Descriptions.Item label="Name">
            {profile.name || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Exchange">
            {profile.exchange || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Sector">
            {profile.sector || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Industry">
            {profile.industry || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Type">
            {profile.type || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Employees">
            {profile.employees || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="CEO">
            {profile.CEO || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Website">
            {profile.website ? (
              <a
                href={profile.website}
                target="_blank"
                rel="noreferrer"
              >
                {profile.website}
              </a>
            ) : "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Phone">
            {profile.phone || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Address" span={3}>
            {[
              profile.address,
              profile.address2,
              profile.city,
              profile.state,
              profile.zip,
              profile.country,
            ]
              .filter(Boolean)
              .join(", ") || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Description" span={3}>
            <div style={{ whiteSpace: "pre-wrap" }}>
              {profile.description || "N/A"}
            </div>
          </Descriptions.Item>
        </Descriptions>
      )}
    </Card>
  );
};

export default ProfileTab;
