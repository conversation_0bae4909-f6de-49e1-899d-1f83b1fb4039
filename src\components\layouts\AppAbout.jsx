import { useTranslation } from "react-i18next";

const AppAbout = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  // Text alignment based on language direction
  const textAlignClass = isRTL ? "text-right" : "text-left";

  return (
    <div className="w-full bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-10">
        {/* Company name and tagline */}
        <div className={`${textAlignClass} mb-8`}>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t("footer.companyName")}
          </h2>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            {t("footer.tagline")}
          </p>
        </div>

        {/* We Provide section */}
        <div className="mb-8">
          <h3
            className={`text-xl font-bold ${textAlignClass} mb-4 text-gray-800 dark:text-white`}
          >
            {t("footer.weProvide")}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              className={`flex items-center ${textAlignClass} gap-2`}
            >
              {/* <span className="bg-gray-700 p-1 rounded">📄</span> */}
              <span className="text-2xl">📊</span>
              <p className="text-gray-700 dark:text-gray-300">
                {t("footer.allData")}
              </p>
            </div>

            <div
              className={`flex items-center ${textAlignClass} gap-2`}
            >
              {/* <span className="bg-gray-700 p-1 rounded">📊</span> */}
              <span className="text-2xl">📑</span>
              <span className="text-gray-700 dark:text-gray-300">
                {t("footer.ratios")}
              </span>
            </div>

            <div
              className={`flex items-center ${textAlignClass} gap-2`}
            >
              {/* <span className="bg-gray-700 p-1 rounded">✓</span> */}
              <span className="text-2xl">📈</span>
              <span className="text-gray-700 dark:text-gray-300">
                {t("footer.dataAccuracy")}
              </span>
            </div>

            <div
              className={`flex items-center ${textAlignClass} gap-2`}
            >
              {/* <span className="bg-gray-700 p-1 rounded">🏢</span> */}
              <span className="text-2xl">💾</span>
              <span className="text-gray-700 dark:text-gray-300">
                {t("footer.companies")}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppAbout;
