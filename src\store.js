import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./redux/user/userSlice";
import { authApi } from "./redux/user/userService";
import stocksReducer from './redux/stocks/stocksSlice';
import filterReducer from "./redux/filter/filterSlice";
import darkModeReducer from "./redux/darkMode/darkModeSlice";
import adminReducer from "./redux/admin/adminSlice";
import watchlistReducer from "./redux/watchlist/watchlistSlice";
import indexReducer from "./redux/index/indexSlice";
import chartReducer from "./redux/chart/chartSlice";
import searchReducer from "./redux/search/searchSlice";
import screenerReducer from "./redux/screener/screenerSlice";
import portfolioReducer from './redux/portfolio/portfolioSlice';
const ENVIRONMENT = import.meta.env.NODE_ENV
export const store = configureStore({
  reducer: {
    user: userReducer,
    index: indexReducer,
    stocks: stocksReducer,
    filters: filterReducer,
    darkMode: darkModeReducer,
    admin: adminReducer,
    chart: chartReducer,
    watchlist: watchlistReducer,
      portfolio: portfolioReducer,
    [authApi.reducerPath]: authApi.reducer,
     search: searchReducer,
    screener: screenerReducer,
  },
  devTools: ENVIRONMENT !== 'production',
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(authApi.middleware),
});
